<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Chart.js Test</title>
    <!-- Use local Chart.js file -->
    <script src="assets/js/chart.min.js"></script>
</head>

<body>
    <h1>Local Chart.js Test</h1>
    <div id="status" style="padding: 10px; margin: 10px 0; background: #f0f0f0;">
        Checking Chart.js...
    </div>

    <div style="width: 400px; height: 400px;">
        <canvas id="myChart"></canvas>
    </div>

    <button onclick="testChart()">Test Chart Creation</button>
    <button onclick="testAPI()">Test API + Chart</button>
    <div id="result" style="margin-top: 10px;"></div>

    <script src="assets/js/apiClient.js?v=3.0"></script>
    <script>
        // Check Chart.js loading
        window.addEventListener('load', function () {
            const status = document.getElementById('status');

            if (typeof Chart !== 'undefined') {
                status.innerHTML = '✅ Chart.js loaded successfully! Version: ' + (Chart.version || 'unknown');
                status.style.background = '#d4edda';
                status.style.color = '#155724';
                console.log('Chart.js available:', Chart);
            } else {
                status.innerHTML = '❌ Chart.js not loaded';
                status.style.background = '#f8d7da';
                status.style.color = '#721c24';
            }
        });

        function testChart() {
            const result = document.getElementById('result');

            try {
                if (typeof Chart === 'undefined') {
                    throw new Error('Chart.js is not available');
                }

                const ctx = document.getElementById('myChart').getContext('2d');

                // Destroy existing chart
                const existingChart = Chart.getChart(ctx);
                if (existingChart) {
                    existingChart.destroy();
                }

                const myChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['0.1', '1', '10', '100', '1000', '15000'],
                        datasets: [{
                            label: 'Test Moisture Content (%)',
                            data: [45, 42, 38, 32, 28, 20],
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Tension (kPa)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Volumetric Water Content (%)'
                                }
                            }
                        }
                    }
                });

                result.innerHTML = '<p style="color: green;">✅ Chart created successfully!</p>';

            } catch (error) {
                result.innerHTML = '<p style="color: red;">❌ Error: ' + error.message + '</p>';
                console.error('Chart creation error:', error);
            }
        }

        async function testAPI() {
            const result = document.getElementById('result');
            result.innerHTML = '<p>🔄 Testing API...</p>';

            try {
                if (typeof Chart === 'undefined') {
                    throw new Error('Chart.js is not available');
                }

                const apiClient = new FlahaSoilAPI();

                const sampleData = {
                    sand: 45,
                    clay: 25,
                    organicMatter: 3.2,
                    densityFactor: 1.35
                };

                const encodedData = btoa(JSON.stringify(sampleData));
                const response = await apiClient.getMoistureTensionCurveDemo(encodedData);

                if (response.success && response.data) {
                    const ctx = document.getElementById('myChart').getContext('2d');

                    // Clear existing chart
                    const existingChart = Chart.getChart(ctx);
                    if (existingChart) {
                        existingChart.destroy();
                    }

                    const tensions = response.data.map(point => point.tension);
                    const moistureContents = response.data.map(point => point.moistureContent);

                    const chart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: tensions,
                            datasets: [{
                                label: 'Moisture Content (%)',
                                data: moistureContents,
                                borderColor: 'rgb(54, 162, 235)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Tension (kPa)'
                                    },
                                    type: 'logarithmic'
                                },
                                y: {
                                    title: {
                                        display: true,
                                        text: 'Volumetric Water Content (%)'
                                    }
                                }
                            }
                        }
                    });

                    result.innerHTML = '<p style="color: green;">✅ API + Chart integration working!</p>' +
                        '<p>Data points: ' + response.data.length + '</p>' +
                        '<p>Demo mode: ' + (response.demo ? 'Yes' : 'No') + '</p>';
                } else {
                    result.innerHTML = '<p style="color: red;">❌ API returned invalid data</p>';
                }

            } catch (error) {
                result.innerHTML = '<p style="color: red;">❌ API test failed: ' + error.message + '</p>';
                console.error('API test error:', error);
            }
        }
    </script>
</body>

</html>