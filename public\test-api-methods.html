<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Methods Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
</head>

<body>
    <div style="padding: 20px;">
        <h1>API Methods Test</h1>
        <div id="status" style="margin-bottom: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
            Ready to test...
        </div>

        <button onclick="testMoistureTension()">Test Moisture-Tension API</button>
        <button onclick="test3DProfile()">Test 3D Profile API</button>
        <button onclick="generateChart()">Generate Chart</button>

        <div style="margin-top: 20px;">
            <canvas id="chart" width="400" height="200"></canvas>
        </div>

        <div id="output"
            style="margin-top: 20px; white-space: pre-wrap; background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace;">
        </div>
    </div>

    <script src="assets/js/apiClient.js?v=20250528152148"></script>
    <script>
        let apiClient;
        let testData = {
            sand: 40,
            clay: 30,
            silt: 30,
            organicMatter: 5,
            densityFactor: 1.3
        };
        let chartData = null;

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.background = type === 'error' ? '#ffebee' : type === 'success' ? '#e8f5e8' : '#f0f0f0';
        }

        document.addEventListener('DOMContentLoaded', function () {
            log('🚀 Initializing API client...');
            try {
                apiClient = new FlahaSoilAPI();
                log('✅ API client initialized successfully');
                updateStatus('API client ready - click buttons to test', 'success');
            } catch (error) {
                log('❌ Failed to initialize API client: ' + error.message);
                updateStatus('Failed to initialize API client', 'error');
            }
        });

        async function testMoistureTension() {
            log('🧪 Testing moisture-tension curve API...');
            updateStatus('Testing moisture-tension API...', 'info');

            try {
                const encodedData = btoa(JSON.stringify(testData));
                log(`📝 Encoded data: ${encodedData.substring(0, 50)}...`);

                const response = await apiClient.getMoistureTensionCurveDemo(encodedData);

                if (response.success) {
                    log(`✅ API call successful!`);
                    log(`📊 Data points received: ${response.data.length}`);
                    log(`📋 Sample data point: ${JSON.stringify(response.data[0])}`);
                    chartData = response.data;
                    updateStatus(`Success! Received ${response.data.length} data points`, 'success');
                } else {
                    log(`❌ API call failed: ${response.error}`);
                    updateStatus('API call failed', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                updateStatus('Error during API call', 'error');
            }
        }

        async function test3DProfile() {
            log('🧪 Testing 3D profile API...');
            updateStatus('Testing 3D profile API...', 'info');

            try {
                const encodedData = btoa(JSON.stringify(testData));
                log(`📝 Encoded data: ${encodedData.substring(0, 50)}...`);

                const response = await apiClient.getSoilProfile3DDemo(encodedData);

                if (response.success) {
                    log(`✅ API call successful!`);
                    log(`📊 Horizons received: ${response.data.horizons.length}`);
                    log(`📋 First horizon: ${JSON.stringify(response.data.horizons[0])}`);
                    updateStatus(`Success! Received profile with ${response.data.horizons.length} horizons`, 'success');
                } else {
                    log(`❌ API call failed: ${response.error}`);
                    updateStatus('API call failed', 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                updateStatus('Error during API call', 'error');
            }
        }

        function generateChart() {
            log('📈 Generating chart...');

            if (!chartData) {
                log('❌ No chart data available. Run moisture-tension test first.');
                updateStatus('No data for chart. Test moisture-tension first.', 'error');
                return;
            }

            try {
                const canvas = document.getElementById('chart');
                const ctx = canvas.getContext('2d');

                // Clear any existing chart
                Chart.getChart(canvas)?.destroy();

                const tensions = chartData.map(point => point.tension);
                const moistureContents = chartData.map(point => point.moistureContent);

                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: tensions,
                        datasets: [{
                            label: 'Moisture Content (%)',
                            data: moistureContents,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Moisture-Tension Curve'
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Tension (kPa)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Volumetric Water Content (%)'
                                }
                            }
                        }
                    }
                });

                log('✅ Chart generated successfully!');
                updateStatus('Chart generated successfully!', 'success');
            } catch (error) {
                log(`❌ Chart generation error: ${error.message}`);
                updateStatus('Chart generation failed', 'error');
            }
        }
    </script>
</body>

</html>