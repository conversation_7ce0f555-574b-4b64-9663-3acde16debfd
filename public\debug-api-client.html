<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Client Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }
    </style>
</head>

<body>
    <h1>🔧 API Client Debug Test</h1>

    <div id="status" class="status info">Loading API client...</div>

    <div>
        <button class="btn-primary" onclick="debugAPIClient()">Debug API Client</button>
        <button class="btn-success" onclick="testMethods()">Test Methods</button>
        <button class="btn-primary" onclick="testBackendDirect()">Test Backend Direct</button>
    </div>

    <div id="results" style="margin-top: 20px;">
        <h3>Debug Results:</h3>
        <pre id="debug-output">Click "Debug API Client" to start...</pre>
    </div>

    <script src="assets/js/apiClient.js?v=3.0"></script>
    <script>
        let apiClient;

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function log(message) {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
        }

        document.addEventListener('DOMContentLoaded', function () {
            log('🚀 DOM Content Loaded');

            try {
                // Check if FlahaSoilAPI class exists
                if (typeof FlahaSoilAPI === 'undefined') {
                    throw new Error('FlahaSoilAPI class is not defined');
                }

                log('✅ FlahaSoilAPI class found');

                // Initialize API client
                apiClient = new FlahaSoilAPI();
                log('✅ API client instantiated successfully');

                // Check base URL
                log(`📍 Base URL: ${apiClient.baseURL}`);

                updateStatus('API Client loaded successfully!', 'success');

            } catch (error) {
                log(`❌ Initialization error: ${error.message}`);
                updateStatus(`Initialization failed: ${error.message}`, 'error');
            }
        });

        function debugAPIClient() {
            log('\n🔍 === API CLIENT DEBUG ===');

            try {
                if (!apiClient) {
                    throw new Error('API client not initialized');
                }

                // Check available methods
                log('📋 Available methods on API client:');
                const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(apiClient))
                    .filter(name => typeof apiClient[name] === 'function' && name !== 'constructor');

                methods.forEach(method => {
                    log(`  - ${method}()`);
                });

                // Specifically check for our demo methods
                log('\n🎯 Checking for demo methods:');

                if (typeof apiClient.getMoistureTensionCurveDemo === 'function') {
                    log('✅ getMoistureTensionCurveDemo() method found');
                } else {
                    log('❌ getMoistureTensionCurveDemo() method NOT found');
                }

                if (typeof apiClient.getSoilProfile3DDemo === 'function') {
                    log('✅ getSoilProfile3DDemo() method found');
                } else {
                    log('❌ getSoilProfile3DDemo() method NOT found');
                }

                updateStatus('Debug complete - check results below', 'info');

            } catch (error) {
                log(`❌ Debug error: ${error.message}`);
                updateStatus(`Debug failed: ${error.message}`, 'error');
            }
        }

        async function testMethods() {
            log('\n🧪 === TESTING API METHODS ===');

            if (!apiClient) {
                log('❌ API client not available');
                return;
            }

            const testData = {
                sand: 40,
                clay: 30,
                silt: 30,
                organicMatter: 5,
                densityFactor: 1.3
            };

            const encodedData = btoa(JSON.stringify(testData));
            log(`📝 Test data encoded: ${encodedData.substring(0, 50)}...`);

            try {
                // Test moisture-tension method
                log('\n🔬 Testing getMoistureTensionCurveDemo...');
                const moistureResponse = await apiClient.getMoistureTensionCurveDemo(encodedData);

                if (moistureResponse.success) {
                    log(`✅ Moisture-tension API successful!`);
                    log(`📊 Data points received: ${moistureResponse.data.length}`);
                } else {
                    log(`❌ Moisture-tension API failed: ${moistureResponse.error}`);
                }

                // Test 3D profile method
                log('\n🔬 Testing getSoilProfile3DDemo...');
                const profileResponse = await apiClient.getSoilProfile3DDemo(encodedData);

                if (profileResponse.success) {
                    log(`✅ 3D Profile API successful!`);
                    log(`📊 Horizons received: ${profileResponse.data.horizons.length}`);
                } else {
                    log(`❌ 3D Profile API failed: ${profileResponse.error}`);
                }

                updateStatus('Method testing complete!', 'success');

            } catch (error) {
                log(`❌ Method test error: ${error.message}`);
                updateStatus(`Method testing failed: ${error.message}`, 'error');
            }
        }

        async function testBackendDirect() {
            log('\n🌐 === TESTING BACKEND DIRECT ===');

            const testData = {
                sand: 40,
                clay: 30,
                silt: 30,
                organicMatter: 5,
                densityFactor: 1.3
            };

            const encodedData = btoa(JSON.stringify(testData));
            const baseURL = 'http://localhost:3001/api/v1';

            try {
                // Test moisture-tension endpoint directly
                log('🔬 Testing moisture-tension endpoint directly...');
                const moistureResponse = await fetch(`${baseURL}/soil/demo/moisture-tension/${encodedData}`);

                if (moistureResponse.ok) {
                    const moistureData = await moistureResponse.json();
                    log(`✅ Direct moisture-tension API successful!`);
                    log(`📊 Response: ${JSON.stringify(moistureData).substring(0, 100)}...`);
                } else {
                    log(`❌ Direct moisture-tension API failed: ${moistureResponse.status}`);
                }

                // Test 3D profile endpoint directly
                log('\n🔬 Testing 3D profile endpoint directly...');
                const profileResponse = await fetch(`${baseURL}/soil/demo/profile-3d/${encodedData}`);

                if (profileResponse.ok) {
                    const profileData = await profileResponse.json();
                    log(`✅ Direct 3D profile API successful!`);
                    log(`📊 Response: ${JSON.stringify(profileData).substring(0, 100)}...`);
                } else {
                    log(`❌ Direct 3D profile API failed: ${profileResponse.status}`);
                }

                updateStatus('Backend direct testing complete!', 'success');

            } catch (error) {
                log(`❌ Backend direct test error: ${error.message}`);
                updateStatus(`Backend testing failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>

</html>