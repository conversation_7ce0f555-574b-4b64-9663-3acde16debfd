<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlahaSoil - Enhanced Debug with Console Ninja</title>
    <!-- Load Chart.js with fallback strategy -->
    <script src="assets/js/chart.min.js"></script>
</head>

<body>
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1>🥷 Console Ninja + Copilot Enhanced Debug</h1>

        <div style="margin: 20px 0;">
            <button onclick="debugChartJS()" style="margin: 5px; padding: 10px;">🔍 Debug Chart.js</button>
            <button onclick="debugAPI()" style="margin: 5px; padding: 10px;">🌐 Debug API Client</button>
            <button onclick="debugVisualization()" style="margin: 5px; padding: 10px;">📊 Debug Full Pipeline</button>
        </div>

        <div id="chart-status" style="padding: 10px; margin: 10px 0; background: #f0f0f0; border-radius: 5px;">
            🔄 Checking Chart.js...
        </div>

        <div id="results" style="margin-top: 20px;">
            <canvas id="debug-chart" width="400" height="200"></canvas>
            <pre id="debug-output"
                style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 10px;"></pre>
        </div>
    </div>

    <script src="assets/js/apiClient.js?v=4.0"></script>
    <script>
        // 🥷 Console Ninja will show these in real-time
        console.log('🚀 FlahaSoil Debug Session Starting...');

        let debugAPIClient;
        let debugData = {
            chartJS: null,
            apiClient: null,
            lastResponse: null,
            errors: [],
            chartJSLoaded: false
        };

        // Chart.js loading status tracker
        function updateChartStatus(message, type = 'info') {
            const statusDiv = document.getElementById('chart-status');
            const colors = {
                info: '#d1ecf1',
                success: '#d4edda',
                error: '#f8d7da',
                warning: '#fff3cd'
            };
            statusDiv.style.background = colors[type] || colors.info;
            statusDiv.textContent = message;
            console.log(`📊 Chart Status: ${message}`);
        }

        // Enhanced Chart.js loader with multiple fallbacks
        async function ensureChartJSLoaded() {
            if (typeof Chart !== 'undefined') {
                debugData.chartJSLoaded = true;
                updateChartStatus('✅ Chart.js already loaded', 'success');
                return true;
            }

            updateChartStatus('🔄 Loading Chart.js...', 'info');

            const fallbackUrls = [
                'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js',
                'https://unpkg.com/chart.js@4.4.0/dist/chart.min.js'
            ];

            for (const url of fallbackUrls) {
                try {
                    console.log(`🔄 Trying to load Chart.js from: ${url}`);
                    await loadChartFromUrl(url);

                    if (typeof Chart !== 'undefined') {
                        debugData.chartJSLoaded = true;
                        debugData.chartJS = Chart;
                        updateChartStatus(`✅ Chart.js loaded from: ${url}`, 'success');
                        return true;
                    }
                } catch (error) {
                    console.warn(`⚠️ Failed to load from ${url}:`, error.message);
                }
            }

            updateChartStatus('❌ Failed to load Chart.js from all sources', 'error');
            return false;
        }

        function loadChartFromUrl(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = () => {
                    console.log(`✅ Successfully loaded Chart.js from: ${url}`);
                    resolve();
                };
                script.onerror = () => {
                    reject(new Error(`Failed to load from ${url}`));
                };
                document.head.appendChild(script);

                // Timeout after 10 seconds
                setTimeout(() => reject(new Error('Load timeout')), 10000);
            });
        }

        // Enhanced initialization with Console Ninja tracking
        document.addEventListener('DOMContentLoaded', async function () {
            console.log('📅 DOM Ready at:', new Date().toISOString());

            try {
                // Initialize API client with error tracking
                debugAPIClient = new FlahaSoilAPI();
                debugData.apiClient = debugAPIClient;
                console.log('✅ API Client initialized:', debugAPIClient);

                // Ensure Chart.js is loaded
                await ensureChartJSLoaded();

            } catch (error) {
                console.error('❌ Initialization error:', error);
                debugData.errors.push({ type: 'init', error: error.message, timestamp: Date.now() });
                updateChartStatus(`❌ Init error: ${error.message}`, 'error');
            }
        });

        // 🔍 Debug Chart.js loading and functionality
        async function debugChartJS() {
            console.group('🔍 Chart.js Debug Session');

            try {
                // Ensure Chart.js is loaded
                const loaded = await ensureChartJSLoaded();
                if (!loaded) {
                    throw new Error('Chart.js could not be loaded');
                }

                // Test basic chart creation
                const ctx = document.getElementById('debug-chart').getContext('2d');

                // Clear any existing chart
                const existingChart = Chart.getChart(ctx);
                if (existingChart) {
                    existingChart.destroy();
                    console.log('🗑️ Destroyed existing chart');
                }

                // Sample data for testing (similar to your moisture-tension curves)
                const testData = {
                    labels: ['0.1', '1', '10', '100', '1500'],
                    datasets: [{
                        label: 'Moisture Content (%)',
                        data: [45, 40, 30, 20, 15],
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                };

                console.log('📊 Creating test chart with data:', testData);

                const chart = new Chart(ctx, {
                    type: 'line',
                    data: testData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '🥷 Console Ninja Debug Chart'
                            }
                        },
                        scales: {
                            x: {
                                title: { display: true, text: 'Tension (kPa)' },
                                type: 'logarithmic'
                            },
                            y: {
                                title: { display: true, text: 'Moisture (%)' }
                            }
                        }
                    }
                });

                console.log('✅ Chart created successfully:', chart);
                debugData.chartJS = Chart;

                document.getElementById('debug-output').textContent =
                    `✅ Chart.js working perfectly!\nChart.js version: ${Chart.version || 'unknown'}\nChart instance: ${chart.constructor.name}`;

            } catch (error) {
                console.error('❌ Chart.js debug error:', error);
                debugData.errors.push({ type: 'chart', error: error.message, timestamp: Date.now() });

                document.getElementById('debug-output').textContent =
                    '❌ Chart.js Error: ' + error.message;
            }

            console.groupEnd();
        }

        // 🌐 Debug API Client functionality (based on your working test patterns)
        async function debugAPI() {
            console.group('🌐 API Client Debug Session');

            try {
                // Test data (matching your working test files)
                const testSoil = {
                    sand: 40,
                    clay: 30,
                    silt: 30,
                    organicMatter: 2.5,
                    densityFactor: 1.3
                };

                console.log('🧪 Testing with soil data:', testSoil);

                const encodedData = btoa(JSON.stringify(testSoil));
                console.log('📝 Encoded data:', encodedData.substring(0, 50) + '...');

                // Test moisture-tension endpoint (using your working pattern)
                console.log('🔬 Testing moisture-tension curve...');
                const moistureResponse = await debugAPIClient.getMoistureTensionCurveDemo(encodedData);
                console.log('📊 Moisture response:', moistureResponse);

                // Test 3D profile endpoint  
                console.log('🔬 Testing 3D soil profile...');
                const profileResponse = await debugAPIClient.getSoilProfile3DDemo(encodedData);
                console.log('🏔️ Profile response:', profileResponse);

                debugData.lastResponse = {
                    moisture: moistureResponse,
                    profile: profileResponse,
                    timestamp: Date.now()
                };

                const moisturePoints = moistureResponse.success ? moistureResponse.data?.length : 'Failed';
                const profileHorizons = profileResponse.success ? profileResponse.data?.horizons?.length : 'Failed';

                document.getElementById('debug-output').textContent =
                    '✅ API Client working!\n' +
                    `Moisture points: ${moisturePoints}\n` +
                    `Profile horizons: ${profileHorizons}\n` +
                    `Demo mode: ${moistureResponse.demo || 'unknown'}`;

            } catch (error) {
                console.error('❌ API debug error:', error);
                debugData.errors.push({ type: 'api', error: error.message, timestamp: Date.now() });

                document.getElementById('debug-output').textContent =
                    '❌ API Error: ' + error.message;
            }

            console.groupEnd();
        }

        // 📊 Debug full visualization pipeline (replicating your advanced-demo.js pattern)
        async function debugVisualization() {
            console.group('📊 Full Pipeline Debug Session');

            try {
                // Step 1: Ensure Chart.js is loaded
                const chartLoaded = await ensureChartJSLoaded();
                if (!chartLoaded) {
                    throw new Error('Chart.js not available');
                }

                // Step 2: Test API
                await debugAPI();

                // Step 3: Create actual visualization (using your working pattern)
                if (debugData.lastResponse?.moisture?.data) {
                    console.log('🎨 Creating moisture-tension visualization...');

                    const data = debugData.lastResponse.moisture.data;
                    const tensions = data.map(point => point.tension);
                    const moistures = data.map(point => point.moistureContent);

                    console.log('📊 Chart data prepared:', {
                        dataPoints: data.length,
                        tensionRange: [Math.min(...tensions), Math.max(...tensions)],
                        moistureRange: [Math.min(...moistures), Math.max(...moistures)]
                    });

                    const ctx = document.getElementById('debug-chart').getContext('2d');

                    // Clear existing chart
                    const existingChart = Chart.getChart(ctx);
                    if (existingChart) {
                        existingChart.destroy();
                    }

                    const chart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: tensions,
                            datasets: [{
                                label: 'Moisture Content (%)',
                                data: moistures,
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: '#1d4ed8',
                                pointBorderColor: '#ffffff',
                                pointBorderWidth: 2,
                                pointRadius: 4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    title: { display: true, text: 'Soil Water Tension (kPa)' },
                                    type: 'logarithmic'
                                },
                                y: {
                                    title: { display: true, text: 'Volumetric Water Content (%)' },
                                    min: 0,
                                    max: Math.max(...moistures) * 1.1
                                }
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: '🥷 Console Ninja + Copilot Visualization'
                                },
                                legend: {
                                    display: true,
                                    position: 'top'
                                }
                            }
                        }
                    });

                    console.log('✅ Full pipeline successful!', chart);

                    document.getElementById('debug-output').textContent =
                        '🎉 Full Pipeline Success!\n' +
                        `Chart points: ${moistures.length}\n` +
                        `Tension range: ${Math.min(...tensions)} - ${Math.max(...tensions)} kPa\n` +
                        `Moisture range: ${Math.min(...moistures).toFixed(1)} - ${Math.max(...moistures).toFixed(1)}%\n` +
                        'Visualization: Active ✅';
                } else {
                    throw new Error('No valid API response data for visualization');
                }

            } catch (error) {
                console.error('❌ Pipeline error:', error);
                debugData.errors.push({ type: 'pipeline', error: error.message, timestamp: Date.now() });

                document.getElementById('debug-output').textContent =
                    '❌ Pipeline Error: ' + error.message;
            }

            console.groupEnd();

            // 🥷 Console Ninja will show this summary in real-time
            console.table(debugData.errors);
            console.log('📊 Debug session summary:', debugData);
        }

        // 🥷 Console Ninja performance tracking
        performance.mark('flaha-debug-start');
        window.addEventListener('load', () => {
            performance.mark('flaha-debug-loaded');
            performance.measure('flaha-load-time', 'flaha-debug-start', 'flaha-debug-loaded');
            console.log('⚡ Page load performance:', performance.getEntriesByType('measure'));
        });
    </script>
</body>

</html>