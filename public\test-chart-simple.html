<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
</head>

<body>
    <h1>Simple Chart.js Test</h1>
    <p>Testing if Chart.js loads and creates a basic chart</p>

    <div style="width: 400px; height: 400px;">
        <canvas id="myChart"></canvas>
    </div>

    <button onclick="testChart()">Test Chart Creation</button>
    <button onclick="testAPI()">Test API + Chart</button>

    <div id="results" style="margin-top: 20px; padding: 10px; background: #f0f0f0;"></div>

    <script src="assets/js/apiClient.js?v=3.0"></script>
    <script>
        // Test basic Chart.js functionality
        function testChart() {
            const results = document.getElementById('results');

            try {
                const ctx = document.getElementById('myChart').getContext('2d');
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                        datasets: [{
                            label: 'Test Data',
                            data: [12, 19, 3, 5, 2],
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });

                results.innerHTML = '<p style="color: green;">✅ Chart.js working correctly!</p>';
            } catch (error) {
                results.innerHTML = '<p style="color: red;">❌ Chart.js error: ' + error.message + '</p>';
                console.error('Chart error:', error);
            }
        }

        // Test API + Chart integration
        async function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>🔄 Testing API...</p>';

            try {
                const apiClient = new FlahaSoilAPI();

                // Create sample data
                const sampleData = {
                    sand: 45,
                    clay: 25,
                    organicMatter: 3.2,
                    densityFactor: 1.35
                };

                const encodedData = btoa(JSON.stringify(sampleData));

                // Test moisture-tension curve API
                const response = await apiClient.getMoistureTensionCurveDemo(encodedData);

                if (response.success && response.data) {
                    // Create chart with API data
                    const ctx = document.getElementById('myChart').getContext('2d');

                    // Clear any existing chart
                    Chart.getChart(ctx)?.destroy();

                    const tensions = response.data.map(point => point.tension);
                    const moistureContents = response.data.map(point => point.moistureContent);

                    const chart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: tensions,
                            datasets: [{
                                label: 'Moisture Content (%)',
                                data: moistureContents,
                                borderColor: 'rgb(54, 162, 235)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Tension (kPa)'
                                    },
                                    type: 'logarithmic'
                                },
                                y: {
                                    title: {
                                        display: true,
                                        text: 'Volumetric Water Content (%)'
                                    }
                                }
                            }
                        }
                    });

                    results.innerHTML = '<p style="color: green;">✅ API + Chart integration working!</p>' +
                        '<p>Data points: ' + response.data.length + '</p>' +
                        '<p>Demo mode: ' + (response.demo ? 'Yes' : 'No') + '</p>';
                } else {
                    results.innerHTML = '<p style="color: red;">❌ API returned invalid data</p>';
                }

            } catch (error) {
                results.innerHTML = '<p style="color: red;">❌ API test failed: ' + error.message + '</p>';
                console.error('API test error:', error);
            }
        }

        // Auto-test when page loads
        window.addEventListener('load', function () {
            console.log('Chart.js version:', Chart.version);
            console.log('Chart object:', Chart);
        });
    </script>
</body>

</html>