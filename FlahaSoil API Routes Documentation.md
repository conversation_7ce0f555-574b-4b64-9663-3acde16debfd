<!-- @format -->

# 🛣️ FlahaSoil API Routes Documentation

**Project**: FlahaSoil - Professional Soil Analysis Platform  
**Version**: v1.0  
**Last Updated**: December 2024  
**Base URL**: `http://localhost:3001/api/v1`

---

## 📋 Table of Contents

1. [Authentication Routes](#-authentication-routes)
2. [Soil Analysis Routes](#-soil-analysis-routes)
3. [Demo Routes](#-demo-routes)
4. [Advanced Visualization Routes](#-advanced-visualization-routes)
5. [Health & Utility Routes](#-health--utility-routes)
6. [Route Access by Plan Tier](#-route-access-by-plan-tier)
7. [Middleware & Security](#-middleware--security)
8. [Error <PERSON>ling](#-error-handling)
9. [Rate Limiting](#-rate-limiting)
10. [Usage Examples](#-usage-examples)

---

## 🔐 Authentication Routes

_Base Path: `/api/v1/auth`_  
_File: [`api-implementation/src/routes/auth.js`](../api-implementation/src/routes/auth.js)_

### User Management

#### Register New User

```http
POST /api/v1/auth/register
```

**Purpose**: Create new user account with plan selection  
**Authentication**: None required  
**Rate Limit**: 5 requests/hour

**Request Body**:

```json
{
	"name": "John Doe",
	"email": "<EMAIL>",
	"password": "SecurePass123!",
	"tier": "FREE" // Optional: FREE, PROFESSIONAL, ENTERPRISE
}
```

**Response**:

```json
{
	"success": true,
	"message": "User registered successfully",
	"user": {
		"id": "user_id",
		"name": "John Doe",
		"email": "<EMAIL>",
		"tier": "FREE",
		"usageCount": 0
	},
	"token": "jwt_token_here"
}
```

#### User Login

```http
POST /api/v1/auth/login
```

**Purpose**: Authenticate user and return JWT token  
**Authentication**: None required  
**Rate Limit**: 10 requests/hour

**Request Body**:

```json
{
	"email": "<EMAIL>",
	"password": "SecurePass123!"
}
```

**Response**:

```json
{
	"success": true,
	"message": "Login successful",
	"user": {
		"id": "user_id",
		"name": "John Doe",
		"email": "<EMAIL>",
		"tier": "PROFESSIONAL",
		"usageCount": 15,
		"usageResetDate": "2024-01-01T00:00:00Z"
	},
	"token": "jwt_token_here"
}
```

#### Get User Profile

```http
GET /api/v1/auth/profile
```

**Purpose**: Retrieve current user's profile information  
**Authentication**: Required (JWT token)  
**Rate Limit**: 100 requests/hour

**Headers**:

```http
Authorization: Bearer jwt_token_here
```

**Response**:

```json
{
	"success": true,
	"user": {
		"id": "user_id",
		"name": "John Doe",
		"email": "<EMAIL>",
		"tier": "PROFESSIONAL",
		"usageCount": 15,
		"usageResetDate": "2024-01-01T00:00:00Z",
		"planSelectedAt": "2023-12-01T10:00:00Z"
	}
}
```

#### Update User Profile

```http
PUT /api/v1/auth/profile
```

**Purpose**: Update user profile information  
**Authentication**: Required (JWT token)  
**Rate Limit**: 20 requests/hour

**Request Body**:

```json
{
	"name": "John Smith",
	"email": "<EMAIL>"
}
```

### Password Management

#### Forgot Password

```http
POST /api/v1/auth/forgot-password
```

**Purpose**: Request password reset email  
**Authentication**: None required  
**Rate Limit**: 3 requests/hour

**Request Body**:

```json
{
	"email": "<EMAIL>"
}
```

#### Reset Password

```http
POST /api/v1/auth/reset-password
```

**Purpose**: Reset password using token from email  
**Authentication**: None required  
**Rate Limit**: 5 requests/hour

**Request Body**:

```json
{
	"token": "reset_token_from_email",
	"newPassword": "NewSecurePass123!"
}
```

#### Change Password

```http
POST /api/v1/auth/change-password
```

**Purpose**: Change password for authenticated user  
**Authentication**: Required (JWT token)  
**Rate Limit**: 10 requests/hour

**Request Body**:

```json
{
	"currentPassword": "CurrentPass123!",
	"newPassword": "NewSecurePass123!"
}
```

### Email Verification

#### Verify Email

```http
POST /api/v1/auth/verify-email
```

**Purpose**: Verify email address with token  
**Authentication**: None required  
**Rate Limit**: 10 requests/hour

**Request Body**:

```json
{
	"token": "verification_token_from_email"
}
```

#### Resend Verification Email

```http
POST /api/v1/auth/resend-verification
```

**Purpose**: Resend email verification  
**Authentication**: Required (JWT token)  
**Rate Limit**: 3 requests/hour

### Plan Management

#### Upgrade User Plan

```http
POST /api/v1/auth/upgrade-plan
```

**Purpose**: Upgrade user to higher plan tier  
**Authentication**: Required (JWT token)  
**Rate Limit**: 5 requests/hour

**Request Body**:

```json
{
	"tier": "PROFESSIONAL" // or "ENTERPRISE"
}
```

---

## 🌱 Soil Analysis Routes

_Base Path: `/api/v1/soil`_  
_File: [`api-implementation/src/routes/soil.js`](../api-implementation/src/routes/soil.js)_

### Basic Analysis

#### Basic Soil Analysis

```http
POST /api/v1/soil/analyze
```

**Purpose**: Perform basic soil texture and water characteristic analysis  
**Authentication**: Required (JWT token)  
**Plan Access**: All tiers (FREE, PROFESSIONAL, ENTERPRISE)  
**Rate Limit**: Plan-based (FREE: 50/month, others: unlimited)

**Request Body**:

```json
{
	"sand": 40,
	"clay": 30,
	"silt": 30,
	"organicMatter": 2.5,
	"densityFactor": 1.0
}
```

**Response**:

```json
{
	"success": true,
	"data": {
		"textureClass": "Loam",
		"fieldCapacity": 28.5,
		"wiltingPoint": 12.3,
		"plantAvailableWater": 16.2,
		"saturation": 47.8,
		"saturatedConductivity": 15.2,
		"recommendations": [
			"Excellent for most crops",
			"Good water holding capacity"
		]
	},
	"usage": {
		"count": 16,
		"limit": 50,
		"resetDate": "2024-01-01T00:00:00Z"
	}
}
```

#### Advanced Soil Analysis

```http
POST /api/v1/soil/analyze/advanced
```

**Purpose**: Perform advanced analysis with regional adjustments  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only  
**Rate Limit**: Unlimited for eligible plans

**Request Body**:

```json
{
	"sand": 40,
	"clay": 30,
	"silt": 30,
	"organicMatter": 2.5,
	"densityFactor": 1.0,
	"regionId": "midwest_us",
	"latitude": 40.7128,
	"longitude": -74.006,
	"elevation": 10.0
}
```

#### Batch Analysis

```http
POST /api/v1/soil/analyze/batch
```

**Purpose**: Process multiple soil samples in one request  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only  
**Rate Limit**: 10 batches/hour

**Request Body**:

```json
{
	"samples": [
		{
			"id": "sample_1",
			"sand": 40,
			"clay": 30,
			"organicMatter": 2.5
		},
		{
			"id": "sample_2",
			"sand": 60,
			"clay": 15,
			"organicMatter": 1.8
		}
	]
}
```

### Data Management

#### Get Analysis History

```http
GET /api/v1/soil/history
```

**Purpose**: Retrieve user's analysis history  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only  
**Rate Limit**: 50 requests/hour

**Query Parameters**:

- `limit`: Number of results (default: 20, max: 100)
- `offset`: Pagination offset (default: 0)
- `startDate`: Filter by start date (ISO 8601)
- `endDate`: Filter by end date (ISO 8601)

**Response**:

```json
{
	"success": true,
	"data": [
		{
			"id": "analysis_id",
			"sand": 40,
			"clay": 30,
			"textureClass": "Loam",
			"fieldCapacity": 28.5,
			"createdAt": "2024-01-15T10:30:00Z"
		}
	],
	"pagination": {
		"total": 45,
		"limit": 20,
		"offset": 0
	}
}
```

#### Export Analysis Data

```http
GET /api/v1/soil/export/:format
```

**Purpose**: Export analysis data in specified format  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only  
**Rate Limit**: 10 requests/hour

**Path Parameters**:

- `format`: Export format (`csv`, `xlsx`, `json`, `pdf`)

**Query Parameters**:

- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `analysisIds`: Specific analysis IDs (comma-separated)

#### Crop Recommendations

```http
POST /api/v1/soil/recommendations
```

**Purpose**: Get crop recommendations based on soil analysis  
**Authentication**: Required (JWT token)  
**Plan Access**: All tiers  
**Rate Limit**: Plan-based

**Request Body**:

```json
{
	"analysisId": "analysis_id_here",
	"climate": "temperate",
	"season": "spring"
}
```

### Enterprise API

#### Enterprise API Endpoint

```http
POST /api/v1/soil/api/analyze
```

**Purpose**: Enterprise API for external integrations  
**Authentication**: Required (API key + JWT token)  
**Plan Access**: ENTERPRISE only  
**Rate Limit**: 1000 requests/hour

**Headers**:

```http
Authorization: Bearer jwt_token_here
X-API-Key: enterprise_api_key_here
```

---

## 🧪 Demo Routes

_Base Path: `/api/v1/soil/demo`_  
_File: [`api-implementation/src/routes/soil.js`](../api-implementation/src/routes/soil.js)_

**Note**: Demo routes are public and don't require authentication. They're designed for showcasing capabilities to potential users.

#### Basic Demo Analysis

```http
POST /api/v1/soil/demo/analyze
```

**Purpose**: Public demo of basic soil analysis  
**Authentication**: None required  
**Rate Limit**: 20 requests/hour per IP

**Request Body**:

```json
{
	"sand": 40,
	"clay": 30,
	"organicMatter": 2.5
}
```

**Response**:

```json
{
	"success": true,
	"data": {
		"textureClass": "Loam",
		"fieldCapacity": 28.5,
		"wiltingPoint": 12.3,
		"plantAvailableWater": 16.2
	},
	"demo": true,
	"note": "Demo analysis - register for full features and unlimited access"
}
```

#### Enhanced Demo Analysis

```http
POST /api/v1/soil/demo/analyze/enhanced
```

**Purpose**: Demo of enhanced analysis with visualizations  
**Authentication**: None required  
**Rate Limit**: 10 requests/hour per IP

---

## 📊 Advanced Visualization Routes

_Base Path: `/api/v1/soil/demo`_  
_File: [`api-implementation/src/routes/soil.js`](../api-implementation/src/routes/soil.js)_

#### Moisture-Tension Curve (Demo)

```http
GET /api/v1/soil/demo/moisture-tension/:encodedData
```

**Purpose**: Generate moisture-tension curve data for visualization  
**Authentication**: None required  
**Rate Limit**: 30 requests/hour per IP

**Path Parameters**:

- `encodedData`: Base64 encoded soil parameters

**Example**:

```bash
# Soil data: {"sand":40,"clay":30,"organicMatter":2.5}
# Encoded: eyJzYW5kIjo0MCwiY2xheSI6MzAsIm9yZ2FuaWNNYXR0ZXIiOjIuNX0=

GET /api/v1/soil/demo/moisture-tension/eyJzYW5kIjo0MCwiY2xheSI6MzAsIm9yZ2FuaWNNYXR0ZXIiOjIuNX0=
```

**Response**:

```json
{
	"success": true,
	"data": [
		{ "tension": 0, "moistureContent": 48.29, "tensionLog": -1 },
		{ "tension": 1, "moistureContent": 47.21, "tensionLog": 0 },
		{ "tension": 3, "moistureContent": 46.41, "tensionLog": 0.477 },
		{ "tension": 10, "moistureContent": 45.12, "tensionLog": 1 },
		{ "tension": 33, "moistureContent": 28.5, "tensionLog": 1.518 },
		{ "tension": 100, "moistureContent": 22.8, "tensionLog": 2 },
		{ "tension": 300, "moistureContent": 18.4, "tensionLog": 2.477 },
		{ "tension": 1500, "moistureContent": 12.3, "tensionLog": 3.176 }
	],
	"metadata": {
		"fieldCapacity": 28.5,
		"wiltingPoint": 12.3,
		"saturation": 48.29,
		"plantAvailableWater": 16.2
	},
	"demo": true
}
```

#### 3D Soil Profile (Demo)

```http
GET /api/v1/soil/demo/profile-3d/:encodedData
```

**Purpose**: Generate 3D soil profile data for visualization  
**Authentication**: None required  
**Rate Limit**: 20 requests/hour per IP

**Response**:

```json
{
	"success": true,
	"data": {
		"horizons": [
			{
				"depth": "0-15cm",
				"texture": "Loam",
				"organicMatter": 3.5,
				"color": "#8B4513",
				"waterHolding": 28.5
			},
			{
				"depth": "15-45cm",
				"texture": "Clay Loam",
				"organicMatter": 2.1,
				"color": "#A0522D",
				"waterHolding": 32.1
			}
		],
		"rootZone": {
			"depth": 60,
			"effectiveness": "High"
		},
		"waterZones": [
			{ "depth": 0, "saturation": 48.3 },
			{ "depth": 30, "saturation": 52.1 },
			{ "depth": 60, "saturation": 55.8 }
		],
		"summary": {
			"totalDepth": 100,
			"rootZoneDepth": 60,
			"averageFieldCapacity": 30.3,
			"averagePlantAvailableWater": 17.8
		}
	},
	"demo": true
}
```

### Authenticated Visualization Routes

#### Moisture-Tension Curve (Authenticated)

```http
GET /api/v1/soil/moisture-tension/:analysisId
```

**Purpose**: Get moisture-tension curve for saved analysis  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

#### 3D Soil Profile (Authenticated)

```http
GET /api/v1/soil/profile-3d/:analysisId
```

**Purpose**: Get 3D soil profile for saved analysis  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

#### Comparative Analysis

```http
POST /api/v1/soil/compare
```

**Purpose**: Compare multiple soil analyses  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

**Request Body**:

```json
{
	"analysisIds": ["analysis_1", "analysis_2", "analysis_3"],
	"parameters": ["fieldCapacity", "wiltingPoint", "plantAvailableWater"]
}
```

#### Real-time Parameter Adjustment

```http
POST /api/v1/soil/adjust-realtime
```

**Purpose**: Adjust parameters and get real-time updates  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

**Request Body**:

```json
{
	"baseAnalysisId": "analysis_id",
	"adjustments": {
		"organicMatter": 3.5,
		"densityFactor": 1.2
	}
}
```

### Regional Data Routes

#### Get Regional Data

```http
GET /api/v1/soil/regional-data/:regionId
```

**Purpose**: Get regional soil data for context  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

#### List Available Regions

```http
GET /api/v1/soil/regions
```

**Purpose**: Get list of available regions  
**Authentication**: Optional  
**Plan Access**: All tiers

**Response**:

```json
{
	"success": true,
	"data": [
		{
			"id": "midwest_us",
			"name": "Midwest United States",
			"description": "Great Plains agricultural region",
			"bbox": {
				"north": 49.0,
				"south": 35.0,
				"east": -80.0,
				"west": -104.0
			}
		}
	]
}
```

#### Enhanced Analysis with Regional Context

```http
POST /api/v1/soil/analyze/enhanced
```

**Purpose**: Analysis with regional adjustments  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

#### Seasonal Variation Data

```http
GET /api/v1/soil/seasonal-variation/:analysisId
```

**Purpose**: Get seasonal variation data for analysis  
**Authentication**: Required (JWT token)  
**Plan Access**: PROFESSIONAL+ only

**Query Parameters**:

- `season`: Target season (`spring`, `summer`, `fall`, `winter`)
- `year`: Target year (default: current year)

---

## 🏥 Health & Utility Routes

_File: [`api-implementation/server.js`](../api-implementation/server.js)_

#### API Health Check

```http
GET /health
```

**Purpose**: Check API server status  
**Authentication**: None required  
**Rate Limit**: None

**Response**:

```json
{
	"status": "healthy",
	"timestamp": "2024-01-15T10:30:00Z",
	"version": "1.0.0",
	"database": "connected",
	"services": {
		"email": "operational",
		"calculations": "operational"
	}
}
```

---

## 🎯 Route Access by Plan Tier

### FREE Tier Access

**Monthly Usage Limit**: 50 calculations

**Available Routes**:

- ✅ All demo endpoints (`/demo/*`)
- ✅ Basic analysis (`/analyze`)
- ✅ Crop recommendations (`/recommendations`)
- ✅ Health check (`/health`)
- ✅ All authentication routes
- ✅ Regional list (`/regions`)

**Restricted Features**:

- ❌ Advanced analysis
- ❌ Batch processing
- ❌ Analysis history
- ❌ Export capabilities
- ❌ Advanced visualizations
- ❌ Comparative analysis

### PROFESSIONAL Tier Access

**Monthly Usage Limit**: Unlimited

**Includes all FREE features plus**:

- ✅ Advanced analysis (`/analyze/advanced`)
- ✅ Batch processing (`/analyze/batch`)
- ✅ Analysis history (`/history`)
- ✅ Export capabilities (`/export/*`)
- ✅ Advanced visualizations (`/moisture-tension/*`, `/profile-3d/*`)
- ✅ Comparative analysis (`/compare`)
- ✅ Real-time adjustments (`/adjust-realtime`)
- ✅ Regional data access (`/regional-data/*`)
- ✅ Enhanced analysis (`/analyze/enhanced`)
- ✅ Seasonal variation (`/seasonal-variation/*`)

**Restricted Features**:

- ❌ Enterprise API access

### ENTERPRISE Tier Access

**Monthly Usage Limit**: Unlimited

**Includes all PROFESSIONAL features plus**:

- ✅ API key access (`/api/analyze`)
- ✅ Cross-user data access (with permissions)
- ✅ Higher rate limits
- ✅ Priority support
- ✅ Custom integrations

---

## 🛡️ Middleware & Security

### Applied Middleware Stack

1. **Rate Limiting** ([`rateLimit.js`](../api-implementation/src/middleware/rateLimit.js))

   - Auth endpoints: 5-10 requests/hour
   - Password reset: 3 requests/hour
   - Email verification: 10 requests/hour
   - Demo endpoints: 20 requests/hour

2. **Authentication** ([`auth.js`](../api-implementation/src/middleware/auth.js))

   - JWT token validation
   - User session management
   - Token refresh handling

3. **Plan Access Control** ([`planAccess.js`](../api-implementation/src/middleware/planAccess.js))

   - Feature access validation
   - Usage limit enforcement
   - Plan-based rate limiting

4. **Input Validation** ([`sanitization.js`](../api-implementation/src/middleware/sanitization.js))

   - XSS protection
   - SQL injection prevention
   - Data type validation

5. **CORS Configuration**
   - Cross-origin request handling
   - Secure header management
   - Environment-specific settings

### Security Headers

All API responses include:

```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

---

## ⚠️ Error Handling

### Standard Error Response Format

```json
{
	"success": false,
	"error": {
		"code": "ERROR_CODE",
		"message": "Human readable error message",
		"details": "Additional error details",
		"timestamp": "2024-01-15T10:30:00Z"
	}
}
```

### Common Error Codes

| HTTP Status | Error Code              | Description                       |
| ----------- | ----------------------- | --------------------------------- |
| 400         | `INVALID_INPUT`         | Invalid request parameters        |
| 401         | `UNAUTHORIZED`          | Invalid or missing authentication |
| 403         | `FORBIDDEN`             | Insufficient permissions          |
| 403         | `PLAN_UPGRADE_REQUIRED` | Feature requires higher plan      |
| 429         | `RATE_LIMIT_EXCEEDED`   | Too many requests                 |
| 429         | `USAGE_LIMIT_EXCEEDED`  | Monthly usage limit reached       |
| 500         | `INTERNAL_ERROR`        | Server error                      |
| 503         | `SERVICE_UNAVAILABLE`   | Service temporarily unavailable   |

### Plan-Specific Errors

```json
{
	"success": false,
	"error": {
		"code": "PLAN_UPGRADE_REQUIRED",
		"message": "This feature requires a PROFESSIONAL plan",
		"details": {
			"requiredPlan": "PROFESSIONAL",
			"currentPlan": "FREE",
			"feature": "advanced_analysis"
		},
		"upgradeUrl": "/upgrade"
	}
}
```

---

## 🚦 Rate Limiting

### Rate Limit Headers

All responses include rate limiting information:

```http
X-RateLimit-Limit: 50
X-RateLimit-Remaining: 23
X-RateLimit-Reset: 1640995200
```

### Plan-Based Rate Limits

| Endpoint Category | FREE     | PROFESSIONAL | ENTERPRISE |
| ----------------- | -------- | ------------ | ---------- |
| Authentication    | 10/hour  | 20/hour      | 50/hour    |
| Basic Analysis    | 50/month | Unlimited    | Unlimited  |
| Advanced Features | N/A      | 1000/hour    | 5000/hour  |
| Demo Endpoints    | 20/hour  | 20/hour      | 20/hour    |
| Export/History    | N/A      | 50/hour      | 200/hour   |

---

## 🧪 Usage Examples

### Complete Authentication Flow

```javascript
// 1. Register new user
const registerResponse = await fetch("/api/v1/auth/register", {
	method: "POST",
	headers: { "Content-Type": "application/json" },
	body: JSON.stringify({
		name: "John Doe",
		email: "<EMAIL>",
		password: "SecurePass123!",
		tier: "FREE",
	}),
});

// 2. Login
const loginResponse = await fetch("/api/v1/auth/login", {
	method: "POST",
	headers: { "Content-Type": "application/json" },
	body: JSON.stringify({
		email: "<EMAIL>",
		password: "SecurePass123!",
	}),
});

const { token } = await loginResponse.json();

// 3. Use authenticated endpoint
const analysisResponse = await fetch("/api/v1/soil/analyze", {
	method: "POST",
	headers: {
		"Content-Type": "application/json",
		Authorization: `Bearer ${token}`,
	},
	body: JSON.stringify({
		sand: 40,
		clay: 30,
		silt: 30,
		organicMatter: 2.5,
	}),
});
```

### Demo Visualization Workflow

```javascript
// 1. Prepare soil data
const soilData = {
	sand: 40,
	clay: 30,
	organicMatter: 2.5,
	densityFactor: 1.0,
};

// 2. Encode for URL
const encodedData = btoa(JSON.stringify(soilData));

// 3. Get moisture-tension curve
const moistureResponse = await fetch(
	`/api/v1/soil/demo/moisture-tension/${encodedData}`
);
const moistureData = await moistureResponse.json();

// 4. Get 3D profile data
const profileResponse = await fetch(
	`/api/v1/soil/demo/profile-3d/${encodedData}`
);
const profileData = await profileResponse.json();

// 5. Use data for Chart.js visualization
const chartData = {
	labels: moistureData.data.map((p) => p.tension),
	datasets: [
		{
			label: "Moisture Content (%)",
			data: moistureData.data.map((p) => p.moistureContent),
			borderColor: "rgb(54, 162, 235)",
			backgroundColor: "rgba(54, 162, 235, 0.2)",
		},
	],
};
```

### Batch Analysis Example

```javascript
// Professional+ tier required
const batchResponse = await fetch("/api/v1/soil/analyze/batch", {
	method: "POST",
	headers: {
		"Content-Type": "application/json",
		Authorization: `Bearer ${token}`,
	},
	body: JSON.stringify({
		samples: [
			{
				id: "field_north",
				sand: 45,
				clay: 25,
				organicMatter: 3.2,
			},
			{
				id: "field_south",
				sand: 35,
				clay: 35,
				organicMatter: 2.8,
			},
			{
				id: "field_west",
				sand: 55,
				clay: 20,
				organicMatter: 2.1,
			},
		],
	}),
});

const batchResults = await batchResponse.json();
// Process results for each field
```

---

## 📚 Integration with Frontend

### API Client Usage

The [`FlahaSoilAPI`](../public/assets/js/apiClient.js) class provides a convenient interface:

```javascript
// Initialize API client
const apiClient = new FlahaSoilAPI();

// Set authentication
apiClient.setAuth(token, userPlan, usageCount);

// Demo analysis
const demoResult = await apiClient.performDemoAnalysis(soilData);

// Authenticated analysis
const realResult = await apiClient.performRealAnalysis(soilData);

// Visualization data
const moistureData = await apiClient.getMoistureTensionCurveDemo(encodedData);
const profileData = await apiClient.getSoilProfile3DDemo(encodedData);
```

### Visualization Integration

Routes designed for [`VisualizationManager`](../public/assets/js/visualizationManager.js):

```javascript
// Initialize visualization manager
const vizManager = new VisualizationManager();

// Create moisture-tension curve
await vizManager.createMoistureTensionCurve(
	"chart-container",
	moistureData.data,
	moistureData.metadata
);

// Create 3D soil profile
await vizManager.create3DSoilProfile("profile-container", profileData.data);
```

---

## 🔄 Data Flow Architecture

```
Frontend Request → Rate Limiting → Authentication → Plan Access Control →
Controller → Service Layer → Database → Response Processing →
Usage Tracking → Response to Frontend
```

### Typical Request Lifecycle

1. **Rate Limiting**: Check request limits per IP/user
2. **Authentication**: Validate JWT token (if required)
3. **Plan Access**: Verify feature access for user's plan
4. **Validation**: Sanitize and validate input data
5. **Controller**: Route to appropriate controller method
6. **Service Layer**: Execute business logic and calculations
7. **Database**: Store/retrieve data as needed
8. **Response**: Format and return standardized response
9. **Usage Tracking**: Update user usage counters
10. **Logging**: Record request for analytics

---

## 🔗 Related Documentation

- [Plan System Documentation](./PLAN_SYSTEM_DOCS.md) - Complete plan tier system
- [Authentication Guide](./AUTHENTICATION_TESTING_GUIDE.md) - Authentication implementation
- [Database Setup](./DATABASE_SETUP.md) - Database schema and setup
- [Testing Guide](./TESTING_GUIDE.md) - API testing procedures
- [Integration Guide](./INTEGRATION_GUIDE.md) - Frontend integration
- [Visualization Guide](./VISUALIZATION_IMPLEMENTATION_COMPLETE.md) - Advanced visualizations

---

**Last Updated**: December 2024  
**API Version**: v1.0  
**Status**: Production Ready ✅
