/**
 * UI Update Functions for FlahaSoil
 * Handles updating quality overview, visualization, and recommendations containers
 */

/**
 * Update quality overview section
 * @param {Object} waterCharacteristics - Soil analysis results
 */
function updateQualityOverview(waterCharacteristics) {
	try {
		// Calculate soil quality score (0-10)
		const qualityScore = calculateSoilQualityScore(waterCharacteristics);
		
		// Update quality score display
		const scoreElement = document.getElementById("soil-quality-score");
		if (scoreElement) {
			scoreElement.textContent = qualityScore.toFixed(1);
		}

		// Update quality indicators
		updateQualityIndicator("drainage-class", getDrainageClass(waterCharacteristics));
		updateQualityIndicator("compaction-risk", getCompactionRisk(waterCharacteristics));
		updateQualityIndicator("erosion-risk", getErosionRisk(waterCharacteristics));

	} catch (error) {
		console.error("Error updating quality overview:", error);
	}
}

/**
 * Update visualization container with water levels
 * @param {Object} waterCharacteristics - Soil analysis results
 */
function updateVisualizationContainer(waterCharacteristics) {
	try {
		const container = document.querySelector(".visualization-container");
		if (!container) return;

		// Update water level positions based on calculated values
		const saturation = parseFloat(waterCharacteristics.saturation) || 0;
		const fieldCapacity = parseFloat(waterCharacteristics.fieldCapacity) || 0;
		const wiltingPoint = parseFloat(waterCharacteristics.wiltingPoint) || 0;

		// Update water level elements
		updateWaterLevel("saturation-level", saturation);
		updateWaterLevel("field-capacity-level", fieldCapacity);
		updateWaterLevel("wilting-point-level", wiltingPoint);

		// Update water zones
		updateWaterZones(saturation, fieldCapacity, wiltingPoint);

	} catch (error) {
		console.error("Error updating visualization container:", error);
	}
}

/**
 * Update recommendations container
 * @param {Object} waterCharacteristics - Soil analysis results
 */
function updateRecommendationsContainer(waterCharacteristics) {
	try {
		// Generate recommendations based on soil characteristics
		const recommendations = generateSoilRecommendations(waterCharacteristics);

		// Update suitable crops list
		updateRecommendationList("suitable-crops-list", recommendations.suitableCrops);
		
		// Update limitations list
		updateRecommendationList("limitations-list", recommendations.limitations);
		
		// Update management tips list
		updateRecommendationList("management-tips-list", recommendations.managementTips);

	} catch (error) {
		console.error("Error updating recommendations container:", error);
	}
}

/**
 * Calculate soil quality score based on water characteristics
 * @param {Object} waterCharacteristics - Soil analysis results
 * @returns {number} Quality score (0-10)
 */
function calculateSoilQualityScore(waterCharacteristics) {
	const paw = parseFloat(waterCharacteristics.plantAvailableWater) || 0;
	const conductivity = parseFloat(waterCharacteristics.saturatedConductivity) || 0;
	
	// Simple scoring algorithm (can be enhanced)
	let score = 5; // Base score
	
	// PAW contribution (0-4 points)
	if (paw > 15) score += 2;
	else if (paw > 10) score += 1;
	else if (paw < 5) score -= 1;
	
	// Conductivity contribution (0-3 points)
	if (conductivity > 10 && conductivity < 100) score += 1.5;
	else if (conductivity < 1 || conductivity > 500) score -= 1;
	
	return Math.max(0, Math.min(10, score));
}

/**
 * Get drainage class based on soil characteristics
 * @param {Object} waterCharacteristics - Soil analysis results
 * @returns {string} Drainage class
 */
function getDrainageClass(waterCharacteristics) {
	const conductivity = parseFloat(waterCharacteristics.saturatedConductivity) || 0;
	
	if (conductivity > 100) return "Excellent";
	if (conductivity > 50) return "Good";
	if (conductivity > 10) return "Moderate";
	if (conductivity > 1) return "Poor";
	return "Very Poor";
}

/**
 * Get compaction risk based on soil characteristics
 * @param {Object} waterCharacteristics - Soil analysis results
 * @returns {string} Compaction risk level
 */
function getCompactionRisk(waterCharacteristics) {
	const clay = parseFloat(document.getElementById("clay-input")?.value) || 0;
	const om = parseFloat(document.getElementById("om-input")?.value) || 0;
	
	if (clay > 40 && om < 2) return "High";
	if (clay > 30 && om < 3) return "Moderate";
	return "Low";
}

/**
 * Get erosion risk based on soil characteristics
 * @param {Object} waterCharacteristics - Soil analysis results
 * @returns {string} Erosion risk level
 */
function getErosionRisk(waterCharacteristics) {
	const sand = parseFloat(document.getElementById("sand-input")?.value) || 0;
	const om = parseFloat(document.getElementById("om-input")?.value) || 0;
	
	if (sand > 70 && om < 2) return "High";
	if (sand > 50 && om < 3) return "Moderate";
	return "Low";
}

/**
 * Update quality indicator display
 * @param {string} elementId - Element ID
 * @param {string} value - Indicator value
 */
function updateQualityIndicator(elementId, value) {
	const element = document.getElementById(elementId);
	if (element) {
		element.textContent = value;
		element.className = `indicator-value ${value.toLowerCase().replace(' ', '-')}`;
	}
}

/**
 * Update water level display
 * @param {string} className - Water level class name
 * @param {number} percentage - Water percentage
 */
function updateWaterLevel(className, percentage) {
	const element = document.querySelector(`.${className}`);
	if (element) {
		// Update position based on percentage (simplified)
		const position = Math.max(10, Math.min(90, 100 - percentage));
		element.style.bottom = `${position}%`;
	}
}

/**
 * Update water zones display
 * @param {number} saturation - Saturation percentage
 * @param {number} fieldCapacity - Field capacity percentage
 * @param {number} wiltingPoint - Wilting point percentage
 */
function updateWaterZones(saturation, fieldCapacity, wiltingPoint) {
	// Update gravitational zone
	const gravitationalZone = document.querySelector(".gravitational-zone");
	if (gravitationalZone) {
		const height = Math.max(0, saturation - fieldCapacity);
		gravitationalZone.style.height = `${height}%`;
	}

	// Update available zone
	const availableZone = document.querySelector(".available-zone");
	if (availableZone) {
		const height = Math.max(0, fieldCapacity - wiltingPoint);
		availableZone.style.height = `${height}%`;
	}

	// Update unavailable zone
	const unavailableZone = document.querySelector(".unavailable-zone");
	if (unavailableZone) {
		unavailableZone.style.height = `${wiltingPoint}%`;
	}
}

/**
 * Generate soil recommendations based on characteristics
 * @param {Object} waterCharacteristics - Soil analysis results
 * @returns {Object} Recommendations object
 */
function generateSoilRecommendations(waterCharacteristics) {
	const textureClass = waterCharacteristics.textureClass || "Unknown";
	const paw = parseFloat(waterCharacteristics.plantAvailableWater) || 0;
	
	const recommendations = {
		suitableCrops: [],
		limitations: [],
		managementTips: []
	};

	// Generate recommendations based on texture class
	switch (textureClass.toLowerCase()) {
		case "clay":
		case "clay loam":
			recommendations.suitableCrops = ["Rice", "Wheat", "Soybeans", "Cotton"];
			recommendations.limitations = ["Poor drainage", "Compaction risk", "Slow water infiltration"];
			recommendations.managementTips = ["Add organic matter", "Avoid working when wet", "Install drainage"];
			break;
		case "sand":
		case "sandy loam":
			recommendations.suitableCrops = ["Carrots", "Potatoes", "Peanuts", "Watermelon"];
			recommendations.limitations = ["Low water retention", "Nutrient leaching", "Erosion risk"];
			recommendations.managementTips = ["Frequent irrigation", "Cover crops", "Organic amendments"];
			break;
		case "loam":
		case "silt loam":
			recommendations.suitableCrops = ["Corn", "Tomatoes", "Beans", "Most vegetables"];
			recommendations.limitations = ["Generally good soil"];
			recommendations.managementTips = ["Maintain organic matter", "Regular soil testing", "Crop rotation"];
			break;
		default:
			recommendations.suitableCrops = ["Consult local extension service"];
			recommendations.limitations = ["Soil analysis needed"];
			recommendations.managementTips = ["Get detailed soil test"];
	}

	return recommendations;
}

/**
 * Update recommendation list display
 * @param {string} listId - List element ID
 * @param {Array} items - List items
 */
function updateRecommendationList(listId, items) {
	const listElement = document.getElementById(listId);
	if (listElement && items.length > 0) {
		listElement.innerHTML = items.map(item => `<li>${item}</li>`).join('');
	}
}
