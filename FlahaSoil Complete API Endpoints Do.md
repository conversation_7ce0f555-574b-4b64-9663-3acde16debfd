<!-- @format -->

# 🌐 FlahaSoil Complete API Endpoints Documentation

## 📋 Table of Contents

1. [Server-Side Endpoints](#server-side-endpoints)
2. [Client-Side API Methods](#client-side-api-methods)
3. [Endpoint Details with Examples](#endpoint-details-with-examples)
4. [Method Mappings](#method-mappings)

---

## 🖥️ Server-Side Endpoints

### Base URL: `http://localhost:3001`

### 🏥 Health & System Endpoints

#### Health Check

```http
GET /health
```

**File**: [`api-implementation/server.js`](api-implementation/server.js)  
**Authentication**: None  
**Description**: Check API server status

**Response**:

```json
{
	"status": "OK",
	"service": "FlahaSoil API"
}
```

---

## 🔐 Authentication Endpoints

### Base Path: `/api/v1/auth`

**File**: [`api-implementation/src/routes/auth.js`](api-implementation/src/routes/auth.js)

#### 1. User Registration

```http
POST /api/v1/auth/register
```

**Controller**: `AuthController.register`  
**Middleware**: Rate limiting (5 requests/hour)  
**Validation**: Email, password, name required

**Request Body**:

```json
{
	"name": "John Doe",
	"email": "<EMAIL>",
	"password": "SecurePass123!",
	"tier": "FREE"
}
```

#### 2. User Login

```http
POST /api/v1/auth/login
```

**Controller**: `AuthController.login`  
**Middleware**: Rate limiting (10 requests/hour)

**Request Body**:

```json
{
	"email": "<EMAIL>",
	"password": "SecurePass123!"
}
```

#### 3. Get User Profile

```http
GET /api/v1/auth/profile
```

**Controller**: `AuthController.getProfile`  
**Middleware**: `authMiddleware` (JWT required)

#### 4. Update User Profile

```http
PUT /api/v1/auth/profile
```

**Controller**: `AuthController.updateProfile`  
**Middleware**: `authMiddleware`, rate limiting (20 requests/hour)

#### 5. Forgot Password

```http
POST /api/v1/auth/forgot-password
```

**Controller**: `AuthController.forgotPassword`  
**Middleware**: Rate limiting (3 requests/hour)

#### 6. Reset Password

```http
POST /api/v1/auth/reset-password
```

**Controller**: `AuthController.resetPassword`  
**Middleware**: Rate limiting (5 requests/hour)

#### 7. Change Password

```http
POST /api/v1/auth/change-password
```

**Controller**: `AuthController.changePassword`  
**Middleware**: `authMiddleware`, rate limiting (10 requests/hour)

#### 8. Verify Email

```http
POST /api/v1/auth/verify-email
```

**Controller**: `AuthController.verifyEmail`  
**Middleware**: Rate limiting (10 requests/hour)

#### 9. Resend Verification Email

```http
POST /api/v1/auth/resend-verification
```

**Controller**: `AuthController.resendVerification`  
**Middleware**: `authMiddleware`, rate limiting (3 requests/hour)

#### 10. Upgrade User Plan

```http
POST /api/v1/auth/upgrade-plan
```

**Controller**: `AuthController.upgradePlan`  
**Middleware**: `authMiddleware`, rate limiting (5 requests/hour)

---

## 🌱 Soil Analysis Endpoints

### Base Path: `/api/v1/soil`

**File**: [`api-implementation/src/routes/soil.js`](api-implementation/src/routes/soil.js)

### 🧪 Demo Endpoints (No Authentication)

#### 1. Basic Demo Analysis

```http
POST /api/v1/soil/demo/analyze
```

**Controller**: `SoilController.analyzeSoilDemo`  
**Middleware**: `soilAnalysisValidation`  
**Rate Limit**: 20 requests/hour per IP

#### 2. Enhanced Demo Analysis

```http
POST /api/v1/soil/demo/analyze/enhanced
```

**Controller**: `EnhancedSoilController.createEnhancedAnalysisDemo`  
**Middleware**: `soilAnalysisValidation`  
**Rate Limit**: 10 requests/hour per IP

#### 3. Demo Moisture-Tension Curve

```http
GET /api/v1/soil/demo/moisture-tension/:encodedData
```

**Controller**: `EnhancedSoilController.getMoistureTensionCurveDemo`  
**Rate Limit**: 30 requests/hour per IP  
**Path Parameter**: `encodedData` - Base64 encoded soil parameters

#### 4. Demo 3D Soil Profile

```http
GET /api/v1/soil/demo/profile-3d/:encodedData
```

**Controller**: `EnhancedSoilController.getSoilProfile3DDemo`  
**Rate Limit**: 20 requests/hour per IP  
**Path Parameter**: `encodedData` - Base64 encoded soil parameters

### 🔒 Authenticated Endpoints

#### 5. Basic Soil Analysis

```http
POST /api/v1/soil/analyze
```

**Controller**: `SoilController.analyzeSoil`  
**Middleware**: `authMiddleware`, `checkUsageLimit()`, `incrementUsage()`, `soilAnalysisValidation`  
**Plan Access**: All tiers (FREE: 50/month limit)

#### 6. Advanced Soil Analysis

```http
POST /api/v1/soil/analyze/advanced
```

**Controller**: `SoilController.analyzeSoilAdvanced`  
**Middleware**: `authMiddleware`, `requireFeature("advancedCalculations")`, `checkUsageLimit()`, `incrementUsage()`, `soilAnalysisValidation`  
**Plan Access**: PROFESSIONAL+ only

#### 7. Batch Analysis

```http
POST /api/v1/soil/analyze/batch
```

**Controller**: `SoilController.analyzeBatch`  
**Middleware**: `authMiddleware`, `requireFeature("batchProcessing")`, `checkUsageLimit()`, `soilAnalysisValidation`  
**Plan Access**: PROFESSIONAL+ only

#### 8. Enhanced Analysis with Regional Context

```http
POST /api/v1/soil/analyze/enhanced
```

**Controller**: `EnhancedSoilController.createEnhancedAnalysis`  
**Middleware**: `authMiddleware`, `requireFeature("enhancedAnalysis")`, `checkUsageLimit()`, `incrementUsage()`  
**Plan Access**: PROFESSIONAL+ only

### 📊 Data Management Endpoints

#### 9. Analysis History

```http
GET /api/v1/soil/history
```

**Controller**: `SoilController.getAnalysisHistory`  
**Middleware**: `authMiddleware`, `requireFeature("analysisHistory")`  
**Plan Access**: PROFESSIONAL+ only  
**Query Parameters**: `limit`, `offset`, `startDate`, `endDate`

#### 10. Export Analysis Data

```http
GET /api/v1/soil/export/:format
```

**Controller**: `SoilController.exportAnalysis`  
**Middleware**: `authMiddleware`, `requireFeature("exportCapabilities")`  
**Plan Access**: PROFESSIONAL+ only  
**Path Parameter**: `format` - csv, xlsx, json, pdf

#### 11. Crop Recommendations

```http
POST /api/v1/soil/recommendations
```

**Controller**: `SoilController.getCropRecommendations`  
**Middleware**: `authMiddleware`, `checkUsageLimit()`  
**Plan Access**: All tiers

### 🏢 Enterprise Endpoints

#### 12. Enterprise API Analysis

```http
POST /api/v1/soil/api/analyze
```

**Controller**: `SoilController.apiAnalyzeSoil`  
**Middleware**: `authMiddleware`, `requireFeature("apiAccess")`, `planBasedRateLimit()`, `soilAnalysisValidation`  
**Plan Access**: ENTERPRISE only  
**Headers**: Requires API key + JWT token

### 📈 Advanced Visualization Endpoints (Professional+)

#### 13. Moisture-Tension Curve Data

```http
GET /api/v1/soil/moisture-tension/:analysisId
```

**Controller**: `EnhancedSoilController.getMoistureTensionCurve`  
**Middleware**: `authMiddleware`, `requireFeature("advancedVisualizations")`  
**Plan Access**: PROFESSIONAL+ only

#### 14. 3D Soil Profile Data

```http
GET /api/v1/soil/profile-3d/:analysisId
```

**Controller**: `EnhancedSoilController.getSoilProfile3D`  
**Middleware**: `authMiddleware`, `requireFeature("profile3D")`  
**Plan Access**: PROFESSIONAL+ only

#### 15. Comparative Analysis

```http
POST /api/v1/soil/compare
```

**Controller**: `EnhancedSoilController.compareAnalyses`  
**Middleware**: `authMiddleware`, `requireFeature("comparativeAnalysis")`, `checkUsageLimit()`  
**Plan Access**: PROFESSIONAL+ only

#### 16. Real-time Parameter Adjustment

```http
POST /api/v1/soil/adjust-realtime
```

**Controller**: `EnhancedSoilController.adjustParametersRealtime`  
**Middleware**: `authMiddleware`, `requireFeature("realtimeAdjustment")`  
**Plan Access**: PROFESSIONAL+ only

### 🌍 Regional Data Endpoints

#### 17. Get Regional Data

```http
GET /api/v1/soil/regional-data/:regionId
```

**Controller**: `EnhancedSoilController.getRegionalData`  
**Middleware**: `authMiddleware`, `requireFeature("regionalData")`  
**Plan Access**: PROFESSIONAL+ only

#### 18. List Available Regions

```http
GET /api/v1/soil/regions
```

**Controller**: `EnhancedSoilController.getAvailableRegions`  
**Middleware**: Optional authentication  
**Plan Access**: All tiers

#### 19. Seasonal Variation Data

```http
GET /api/v1/soil/seasonal-variation/:analysisId
```

**Controller**: `EnhancedSoilController.getSeasonalVariation`  
**Middleware**: `authMiddleware`, `requireFeature("seasonalAnalysis")`  
**Plan Access**: PROFESSIONAL+ only

---

## 💻 Client-Side API Methods

### File: [`public/assets/js/apiClient.js`](public/assets/js/apiClient.js)

### 🏗️ Constructor & Setup Methods

#### FlahaSoilAPI Constructor

```javascript
constructor();
```

**Description**: Initialize API client with base URL, token, online status  
**Properties Set**: `baseURL`, `token`, `isOnline`, `userPlan`, `usageCount`, `maxFreeUsage`

#### Authentication Management

```javascript
setAuth(token, (userPlan = "FREE"), (usageCount = 0));
clearAuth();
hasExceededFreeLimit();
incrementUsage();
getRemainingFreeCalculations();
```

### 🔬 Core Analysis Methods

#### 1. Basic Soil Analysis

```javascript
async analyzeSoil(soilData)
```

**Maps to**: `POST /api/v1/soil/analyze`  
**Parameters**:

```javascript
{
  sand: number,
  clay: number,
  organicMatter: number,
  densityFactor: number
}
```

**Returns**: Analysis results with usage info

#### 2. Advanced Soil Analysis

```javascript
async analyzeSoilAdvanced(soilData)
```

**Maps to**: `POST /api/v1/soil/analyze/advanced`  
**Plan Required**: PROFESSIONAL+  
**Features**: Enhanced calculations with regional adjustments

#### 3. Batch Analysis

```javascript
async analyzeBatch(soilDataArray)
```

**Maps to**: `POST /api/v1/soil/analyze/batch`  
**Plan Required**: PROFESSIONAL+  
**Parameters**: Array of soil data objects

#### 4. Demo Analysis

```javascript
async performDemoAnalysis(soilData)
```

**Maps to**: `POST /api/v1/soil/demo/analyze`  
**Authentication**: Not required  
**Description**: Public demo functionality

#### 5. Enhanced Demo Analysis

```javascript
async performEnhancedDemoAnalysis(soilData)
```

**Maps to**: `POST /api/v1/soil/demo/analyze/enhanced`  
**Authentication**: Not required  
**Features**: Advanced demo with regional context

### 📊 Data Management Methods

#### 6. Analysis History

```javascript
async getAnalysisHistory(page = 1, limit = 10)
```

**Maps to**: `GET /api/v1/soil/history`  
**Plan Required**: PROFESSIONAL+  
**Parameters**: Pagination support

#### 7. Export Analysis

```javascript
async exportAnalysis(analysisId, format = "csv")
```

**Maps to**: `GET /api/v1/soil/export/:format`  
**Plan Required**: PROFESSIONAL+  
**Formats**: csv, xlsx, json, pdf

#### 8. Crop Recommendations

```javascript
async getCropRecommendations(soilData)
```

**Maps to**: `POST /api/v1/soil/recommendations`  
**Plan Access**: All tiers

### 🔐 Authentication Methods

#### 9. User Registration

```javascript
async register(email, password, name)
```

**Maps to**: `POST /api/v1/auth/register`  
**Description**: Create new user account

#### 10. User Login

```javascript
async login(email, password)
```

**Maps to**: `POST /api/v1/auth/login`  
**Description**: Authenticate user and get JWT token

#### 11. User Logout

```javascript
logout();
```

**Description**: Clear local authentication data

#### 12. Plan Upgrade

```javascript
async upgradePlan(plan)
```

**Maps to**: `POST /api/v1/auth/upgrade-plan`  
**Parameters**: "PROFESSIONAL" or "ENTERPRISE"

### 📈 Advanced Visualization Methods

#### 13. Moisture-Tension Curve (Authenticated)

```javascript
async getMoistureTensionCurve(analysisId)
```

**Maps to**: `GET /api/v1/soil/moisture-tension/:analysisId`  
**Plan Required**: PROFESSIONAL+

#### 14. 3D Soil Profile (Authenticated)

```javascript
async getSoilProfile3D(analysisId)
```

**Maps to**: `GET /api/v1/soil/profile-3d/:analysisId`  
**Plan Required**: PROFESSIONAL+

#### 15. Demo Moisture-Tension Curve

```javascript
async getMoistureTensionCurveDemo(encodedData)
```

**Maps to**: `GET /api/v1/soil/demo/moisture-tension/:encodedData`  
**Authentication**: Not required

#### 16. Demo 3D Soil Profile

```javascript
async getSoilProfile3DDemo(encodedData)
```

**Maps to**: `GET /api/v1/soil/demo/profile-3d/:encodedData`  
**Authentication**: Not required

### 🌍 Regional Data Methods

#### 17. Regional Soil Data

```javascript
async getRegionalData(regionId)
```

**Maps to**: `GET /api/v1/soil/regional-data/:regionId`  
**Plan Required**: PROFESSIONAL+

#### 18. Available Regions

```javascript
async getAvailableRegions()
```

**Maps to**: `GET /api/v1/soil/regions`  
**Plan Access**: All tiers

#### 19. Enhanced Analysis with Regional Context

```javascript
async createEnhancedAnalysis(analysisData)
```

**Maps to**: `POST /api/v1/soil/analyze/enhanced`  
**Plan Required**: PROFESSIONAL+

### 🔍 Feature Access Methods

#### 20. Check Advanced Visualization Access

```javascript
hasAdvancedVisualizationAccess();
```

**Returns**: Boolean based on user plan

#### 21. Get Visualization Features

```javascript
getVisualizationFeatures();
```

**Returns**: Object with available features by plan

---

## 📋 Endpoint Details with Examples

### 🧪 Demo Analysis Example

#### Client Method Call:

```javascript
const apiClient = new FlahaSoilAPI();
const result = await apiClient.performDemoAnalysis({
	sand: 40,
	clay: 30,
	organicMatter: 2.5,
	densityFactor: 1.0,
});
```

#### Server Endpoint:

```http
POST /api/v1/soil/demo/analyze
Content-Type: application/json

{
  "sand": 40,
  "clay": 30,
  "organicMatter": 2.5,
  "densityFactor": 1.0
}
```

#### Response:

```json
{
	"success": true,
	"data": {
		"textureClass": "Loam",
		"fieldCapacity": 28.5,
		"wiltingPoint": 12.3,
		"plantAvailableWater": 16.2,
		"saturation": 47.8,
		"saturatedConductivity": 15.2
	},
	"demo": true,
	"note": "Demo mode - register for full features"
}
```

### 🔐 Authentication Example

#### Client Method Call:

```javascript
const result = await apiClient.login("<EMAIL>", "password123");
if (result.success) {
	// Token automatically stored
	console.log("Logged in as:", result.user.name);
}
```

#### Server Endpoint:

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 📊 Visualization Data Example

#### Client Method Call:

```javascript
// Encode soil data for demo endpoint
const soilData = { sand: 40, clay: 30, organicMatter: 2.5 };
const encodedData = btoa(JSON.stringify(soilData));
const result = await apiClient.getMoistureTensionCurveDemo(encodedData);
```

#### Server Endpoint:

```http
GET /api/v1/soil/demo/moisture-tension/eyJzYW5kIjo0MCwiY2xheSI6MzB9
```

#### Response:

```json
{
	"success": true,
	"data": [
		{ "tension": 0, "moistureContent": 48.29, "tensionLog": -1 },
		{ "tension": 1, "moistureContent": 47.21, "tensionLog": 0 },
		{ "tension": 33, "moistureContent": 28.5, "tensionLog": 1.518 },
		{ "tension": 1500, "moistureContent": 12.3, "tensionLog": 3.176 }
	],
	"metadata": {
		"fieldCapacity": 28.5,
		"wiltingPoint": 12.3,
		"saturation": 48.29
	},
	"demo": true
}
```

---

## 🔗 Method Mappings Summary

| Client Method                   | Server Endpoint                                | Authentication | Plan Required     |
| ------------------------------- | ---------------------------------------------- | -------------- | ----------------- |
| `analyzeSoil()`                 | `POST /api/v1/soil/analyze`                    | Required       | All (with limits) |
| `analyzeSoilAdvanced()`         | `POST /api/v1/soil/analyze/advanced`           | Required       | PROFESSIONAL+     |
| `analyzeBatch()`                | `POST /api/v1/soil/analyze/batch`              | Required       | PROFESSIONAL+     |
| `performDemoAnalysis()`         | `POST /api/v1/soil/demo/analyze`               | None           | Public            |
| `getMoistureTensionCurveDemo()` | `GET /api/v1/soil/demo/moisture-tension/:data` | None           | Public            |
| `getSoilProfile3DDemo()`        | `GET /api/v1/soil/demo/profile-3d/:data`       | None           | Public            |
| `getAnalysisHistory()`          | `GET /api/v1/soil/history`                     | Required       | PROFESSIONAL+     |
| `exportAnalysis()`              | `GET /api/v1/soil/export/:format`              | Required       | PROFESSIONAL+     |
| `getCropRecommendations()`      | `POST /api/v1/soil/recommendations`            | Required       | All               |
| `login()`                       | `POST /api/v1/auth/login`                      | None           | Public            |
| `register()`                    | `POST /api/v1/auth/register`                   | None           | Public            |
| `upgradePlan()`                 | `POST /api/v1/auth/upgrade-plan`               | Required       | All               |
| `getRegionalData()`             | `GET /api/v1/soil/regional-data/:id`           | Required       | PROFESSIONAL+     |
| `getAvailableRegions()`         | `GET /api/v1/soil/regions`                     | Optional       | All               |
| `createEnhancedAnalysis()`      | `POST /api/v1/soil/analyze/enhanced`           | Required       | PROFESSIONAL+     |

---

## 🎯 Plan-Based Feature Matrix

| Feature           | FREE          | PROFESSIONAL | ENTERPRISE   |
| ----------------- | ------------- | ------------ | ------------ |
| Basic Analysis    | ✅ (50/month) | ✅ Unlimited | ✅ Unlimited |
| Demo Endpoints    | ✅            | ✅           | ✅           |
| Advanced Analysis | ❌            | ✅           | ✅           |
| Batch Processing  | ❌            | ✅           | ✅           |
| Analysis History  | ❌            | ✅           | ✅           |
| Export Data       | ❌            | ✅           | ✅           |
| Visualizations    | ❌            | ✅           | ✅           |
| Regional Data     | ❌            | ✅           | ✅           |
| API Access        | ❌            | ❌           | ✅           |

---

## 🔧 Usage Examples

### Complete Authentication Flow

```javascript
const api = new FlahaSoilAPI();

// 1. Register
const registerResult = await api.register(
	"<EMAIL>",
	"password123",
	"John Doe"
);

// 2. Login (token stored automatically)
const loginResult = await api.login("<EMAIL>", "password123");

// 3. Perform analysis
const analysisResult = await api.analyzeSoil({
	sand: 40,
	clay: 30,
	organicMatter: 2.5,
	densityFactor: 1.0,
});

// 4. Check usage
console.log("Remaining calculations:", api.getRemainingFreeCalculations());
```

### Visualization Workflow

```javascript
// Demo visualization (no auth)
const soilData = { sand: 40, clay: 30, organicMatter: 2.5 };
const encodedData = btoa(JSON.stringify(soilData));

const moistureData = await api.getMoistureTensionCurveDemo(encodedData);
const profileData = await api.getSoilProfile3DDemo(encodedData);

// Professional visualization (auth required)
if (api.hasAdvancedVisualizationAccess()) {
	const realMoistureData = await api.getMoistureTensionCurve("analysis_id");
	const realProfileData = await api.getSoilProfile3D("analysis_id");
}
```

### Batch Processing Example

```javascript
// Professional+ feature
const batchData = [
	{ sand: 45, clay: 25, organicMatter: 3.2 },
	{ sand: 35, clay: 35, organicMatter: 2.8 },
	{ sand: 55, clay: 20, organicMatter: 2.1 },
];

const batchResult = await api.analyzeBatch(batchData);
if (batchResult.success) {
	batchResult.results.forEach((result, index) => {
		console.log(`Sample ${index + 1}:`, result.textureClass);
	});
}
```

**Last Updated**: December 2024  
**API Version**: v1.0  
**Status**: Production Ready ✅
