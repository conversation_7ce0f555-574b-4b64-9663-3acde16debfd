<!DOCTYPE HTML>
<html>

<head>
  <title>FlahaSoil - Soil Texture Analysis | Flaha Agri Tech PA</title>
  <meta name="description"
    content="Advanced soil texture analysis tool by Flaha Agri Tech Precision Agriculture division">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" type="image/x-icon" href="./assets/img/favicon.ico">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600&display=swap"
    rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="./assets/css/style.css">
  <!-- Update to D3.js v7 -->
  <script src="https://d3js.org/d3.v7.min.js"></script>
</head>

<body>
  <!-- Flaha PA Header -->
  <header class="flaha-header">
    <div class="header-content">
      <div class="brand-section">
        <div class="flaha-logo">
          <a href="./landing.html" class="logo-link">
            <span class="flaha-text">FLAHA</span>
            <span class="pa-text">PA</span>
          </a>
        </div>
        <div class="brand-tagline">Precision Agriculture</div>
      </div>
      <div class="app-title-section">
        <h1 class="app-title">FlahaSoil</h1>
        <p class="app-subtitle">Advanced Soil Texture Analysis</p>
      </div>
      <div class="header-actions">
        <div class="nav-links">
          <a href="./index.html" class="nav-link active">Soil Analysis</a>
          <a href="./demo.html" class="nav-link">Basic Demo</a>
          <a href="./advanced-demo.html" class="nav-link" id="advancedDemoLink">Advanced Features</a>
          <a href="./profile.html" class="nav-link" id="profileLink" style="display: none;">Profile</a>
        </div>
        <div class="auth-section" id="authSection">
          <button class="btn-login" onclick="showLoginModal()">Login</button>
          <button class="btn-signup" onclick="showSignupModal()">Sign Up</button>
        </div>
        <div class="user-section" id="userSection" style="display: none;">
          <!-- Plan Status Display -->
          <div class="plan-status-container" id="planStatusContainer">
            <div class="plan-badge" id="planBadge">FREE</div>
            <div class="usage-counter" id="usageCounter" style="display: none;">
              <span class="usage-text">Usage: <span id="usageCount">0</span>/<span id="usageLimit">50</span></span>
            </div>
          </div>

          <div class="user-menu">
            <button class="btn-user" onclick="toggleUserDropdown()">
              <span class="user-name" id="headerUserName">User</span>
              <span class="dropdown-arrow">▼</span>
            </button>
            <div class="user-dropdown" id="userDropdown">
              <a href="./profile.html" class="dropdown-item">Profile</a>
              <a href="./index.html" class="dropdown-item">Soil Analysis</a>
              <a href="#" class="dropdown-item" onclick="showPlanUpgradePrompt()">Upgrade Plan</a>
              <a href="#" class="dropdown-item" onclick="logout()">Logout</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <main class="main-content">
    <div class="input-container">
      <div class="input-group">
        <label for="clay-input">Clay %:</label>
        <input type="number" id="clay-input" min="0" max="100" value="33">
      </div>
      <div class="input-group">
        <label for="sand-input">Sand %:</label>
        <input type="number" id="sand-input" min="0" max="100" value="33">
      </div>
      <div class="input-group">
        <label for="silt-input" class="disabled-label">Silt %:</label>
        <input type="number" id="silt-input" disabled value="34">
      </div>
      <button id="update-point">Update Point</button>

      <!-- Advanced Actions (Plan-based) -->
      <div class="advanced-actions" id="advancedActions" style="display: none;">
        <div class="action-buttons">
          <button class="btn-secondary" id="batchAnalysisBtn" onclick="showBatchAnalysis()" style="display: none;">
            <span class="btn-icon">📊</span>
            Batch Analysis
          </button>
          <button class="btn-secondary" id="historyBtn" onclick="showAnalysisHistory()" style="display: none;">
            <span class="btn-icon">📋</span>
            Analysis History
          </button>
          <button class="btn-secondary" id="exportBtn" onclick="exportCurrentAnalysis()" style="display: none;">
            <span class="btn-icon">💾</span>
            Export Results
          </button>
        </div>
      </div>
    </div>

    <div class="advanced-inputs">
      <h3>Soil Properties</h3>

      <!-- Basic Properties (Always Visible) -->
      <div class="input-section basic-properties">
        <h4>Basic Properties</h4>
        <div class="input-row">
          <div class="input-group">
            <label for="om-input">Organic Matter %:</label>
            <input type="number" id="om-input" min="0" max="8" step="0.1" value="2.5">
            <span class="input-help">Typical range: 1-6%</span>
          </div>
          <div class="input-group">
            <label for="density-input">Bulk Density (g/cm³):</label>
            <input type="number" id="density-input" min="0.9" max="1.8" step="0.05" value="1.3">
            <span class="input-help">Sandy: 1.4-1.8, Clay: 1.0-1.4</span>
          </div>
        </div>
      </div>

      <!-- Professional Tier Features -->
      <div class="input-section professional-features" id="professionalFeatures" style="display: none;">
        <h4>
          Professional Features
          <span class="tier-badge professional">PRO</span>
          <button class="toggle-section" onclick="toggleSection('professionalInputs')">
            <span id="professionalToggle">▼</span>
          </button>
        </h4>
        <div class="feature-upgrade-overlay" id="professionalUpgradeOverlay">
          <div class="upgrade-content">
            <div class="upgrade-icon">🔒</div>
            <h4>Professional Features</h4>
            <p>Unlock advanced soil parameters and detailed analysis</p>
            <button class="btn-upgrade-feature" onclick="showPlanUpgradePrompt('PROFESSIONAL')">
              Upgrade to Professional
            </button>
          </div>
        </div>
        <div class="collapsible-content" id="professionalInputs">
          <div class="input-row">
            <div class="input-group">
              <label for="gravel-input">Gravel Content %:</label>
              <input type="number" id="gravel-input" min="0" max="80" step="1" value="0">
              <span class="input-help">Rock fragments >2mm</span>
            </div>
            <div class="input-group">
              <label for="ec-input">Electrical Conductivity (dS/m):</label>
              <input type="number" id="ec-input" min="0" max="20" step="0.1" value="0.5">
              <span class="input-help">Salinity measure</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Expert Mode Toggle -->
      <div class="expert-mode-toggle">
        <label class="toggle-switch">
          <input type="checkbox" id="expertMode" onchange="toggleExpertMode()">
          <span class="slider"></span>
          Expert Mode
        </label>
        <span class="expert-help">Show all parameters and scientific details</span>
      </div>

      <!-- Expert Mode Features (Hidden by default) -->
      <div class="input-section expert-features" id="expertFeatures" style="display: none;">
        <h4>
          Expert Parameters
          <span class="tier-badge expert">EXPERT</span>
        </h4>

        <!-- Environmental Conditions -->
        <div class="parameter-group">
          <h5>Environmental Conditions</h5>
          <div class="input-row">
            <div class="input-group">
              <label for="soil-temp-input">Soil Temperature (°C):</label>
              <input type="number" id="soil-temp-input" min="0" max="40" step="0.5" value="20">
              <span class="input-help">Affects biological activity</span>
            </div>
            <div class="input-group">
              <label for="climate-zone-input">Climate Zone:</label>
              <select id="climate-zone-input">
                <option value="arid">Arid</option>
                <option value="semi-arid">Semi-arid</option>
                <option value="temperate" selected>Temperate</option>
                <option value="tropical">Tropical</option>
                <option value="mediterranean">Mediterranean</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Physical Structure -->
        <div class="parameter-group">
          <h5>Physical Structure</h5>
          <div class="input-row">
            <div class="input-group">
              <label for="aggregate-stability-input">Aggregate Stability %:</label>
              <input type="number" id="aggregate-stability-input" min="0" max="100" step="1" value="75">
              <span class="input-help">Soil structure quality</span>
            </div>
            <div class="input-group">
              <label for="slope-input">Slope Gradient (%):</label>
              <input type="number" id="slope-input" min="0" max="45" step="0.5" value="2">
              <span class="input-help">Affects erosion risk</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="chart-container"></div>
    <div id="coordinates"></div>
    <div id="soil-texture-display"></div>

    <div class="results-container">
      <h3>Soil Analysis Results</h3>

      <!-- Connection Status -->
      <div class="connection-status" id="connectionStatus" style="display: none;">
        <div class="status-message">
          <span class="status-icon">🌐</span>
          <span class="status-text">FlahaSoil requires an internet connection for advanced soil analysis
            calculations.</span>
          <button class="retry-button" onclick="retryConnection()">Retry Connection</button>
        </div>
      </div>

      <!-- Soil Quality Overview -->
      <div class="quality-overview">
        <div class="quality-score">
          <div class="score-circle">
            <div class="score-value" id="soil-quality-score">-</div>
            <div class="score-label">Soil Quality Index</div>
          </div>
        </div>
        <div class="quality-indicators">
          <div class="indicator">
            <span class="indicator-label">Drainage:</span>
            <span id="drainage-class" class="indicator-value">-</span>
          </div>
          <div class="indicator">
            <span class="indicator-label">Compaction Risk:</span>
            <span id="compaction-risk" class="indicator-value">-</span>
          </div>
          <div class="indicator">
            <span class="indicator-label">Erosion Risk:</span>
            <span id="erosion-risk" class="indicator-value">-</span>
          </div>
        </div>
      </div>

      <!-- Core Water Characteristics -->
      <h4>Water Characteristics</h4>
      <div class="results-grid">
        <div class="result-item">
          <div class="result-label">Field Capacity:</div>
          <div id="field-capacity" class="result-value">-</div>
          <div class="result-unit">%</div>
          <div class="progress-bar-container">
            <div id="field-capacity-bar" class="progress-bar"></div>
          </div>
          <div class="confidence-info" id="fc-confidence" style="display: none;">
            R² = 0.63, SE = ±0.05
          </div>
        </div>
        <div class="result-item">
          <div class="result-label">Wilting Point:</div>
          <div id="wilting-point" class="result-value">-</div>
          <div class="result-unit">%</div>
          <div class="progress-bar-container">
            <div id="wilting-point-bar" class="progress-bar"></div>
          </div>
          <div class="confidence-info" id="wp-confidence" style="display: none;">
            R² = 0.86, SE = ±0.02
          </div>
        </div>
        <div class="result-item">
          <div class="result-label">Plant Available Water:</div>
          <div id="plant-available-water" class="result-value">-</div>
          <div class="result-unit">%</div>
          <div class="progress-bar-container">
            <div id="plant-available-water-bar" class="progress-bar"></div>
          </div>
        </div>
        <div class="result-item">
          <div class="result-label">Saturation:</div>
          <div id="saturation" class="result-value">-</div>
          <div class="result-unit">%</div>
          <div class="progress-bar-container">
            <div id="saturation-bar" class="progress-bar"></div>
          </div>
          <div class="confidence-info" id="sat-confidence" style="display: none;">
            R² = 0.29, SE = ±0.04
          </div>
        </div>
        <div class="result-item">
          <div class="result-label">Saturated Conductivity:</div>
          <div id="saturated-conductivity" class="result-value">-</div>
          <div class="result-unit">mm/hr</div>
          <div class="progress-bar-container">
            <div id="saturated-conductivity-bar" class="progress-bar"></div>
          </div>
        </div>
      </div>

      <!-- Professional Tier Results -->
      <div class="professional-results" id="professionalResults" style="display: none;">
        <h4>
          Advanced Parameters
          <span class="tier-badge professional">PRO</span>
        </h4>
        <div class="feature-upgrade-overlay" id="professionalResultsUpgradeOverlay">
          <div class="upgrade-content">
            <div class="upgrade-icon">🔒</div>
            <h4>Professional Analysis</h4>
            <p>Get detailed soil parameters and advanced calculations</p>
            <button class="btn-upgrade-feature" onclick="showPlanUpgradePrompt('PROFESSIONAL')">
              Upgrade to Professional
            </button>
          </div>
        </div>
        <div class="results-grid">
          <div class="result-item">
            <div class="result-label">Air-Entry Tension:</div>
            <div id="air-entry-tension" class="result-value">-</div>
            <div class="result-unit">kPa</div>
            <div class="confidence-info" id="aet-confidence" style="display: none;">
              R² = 0.78, SE = ±2.9
            </div>
          </div>
          <div class="result-item">
            <div class="result-label">Bulk Density:</div>
            <div id="bulk-density" class="result-value">-</div>
            <div class="result-unit">g/cm³</div>
          </div>
          <div class="result-item">
            <div class="result-label">Lambda (λ):</div>
            <div id="lambda-value" class="result-value">-</div>
            <div class="result-unit">-</div>
          </div>
        </div>
      </div>

      <!-- Enterprise Tier Results -->
      <div class="enterprise-results" id="enterpriseResults" style="display: none;">
        <h4>
          Gravel & Salinity Effects
          <span class="tier-badge enterprise">ENTERPRISE</span>
        </h4>
        <div class="feature-upgrade-overlay" id="enterpriseResultsUpgradeOverlay">
          <div class="upgrade-content">
            <div class="upgrade-icon">🔒</div>
            <h4>Enterprise Analysis</h4>
            <p>Advanced gravel and salinity analysis for professional farming</p>
            <button class="btn-upgrade-feature" onclick="showPlanUpgradePrompt('ENTERPRISE')">
              Upgrade to Enterprise
            </button>
          </div>
        </div>
        <div class="results-grid">
          <div class="result-item">
            <div class="result-label">Bulk PAW:</div>
            <div id="bulk-paw" class="result-value">-</div>
            <div class="result-unit">%</div>
          </div>
          <div class="result-item">
            <div class="result-label">Bulk Conductivity:</div>
            <div id="bulk-conductivity" class="result-value">-</div>
            <div class="result-unit">mm/hr</div>
          </div>
          <div class="result-item">
            <div class="result-label">Osmotic Potential:</div>
            <div id="osmotic-potential" class="result-value">-</div>
            <div class="result-unit">kPa</div>
          </div>
        </div>
      </div>

      <div class="water-visualization">
        <h4>Soil Water Content Visualization</h4>
        <div class="visualization-container">
          <div class="water-container">
            <div class="water-level saturation-level">
              <span>Saturation</span>
            </div>
            <div class="water-level field-capacity-level">
              <span>Field Capacity</span>
            </div>
            <div class="water-level wilting-point-level">
              <span>Wilting Point</span>
            </div>

            <!-- Water zones with labels -->
            <div class="water-zone gravitational-zone">
              <div class="zone-label">Gravitational Water</div>
            </div>
            <div class="water-zone available-zone">
              <div class="zone-label">Plant Available Water</div>
            </div>
            <div class="water-zone unavailable-zone">
              <div class="zone-label">Unavailable Water</div>
            </div>
          </div>

          <div class="water-explanation">
            <h5>Understanding Soil Water Zones</h5>
            <ul>
              <li><strong>Gravitational Water:</strong> Drains quickly from soil due to gravity. Not available to plants
                for long.</li>
              <li><strong>Plant Available Water:</strong> Water that plants can use. The wider this zone, the better for
                plant growth.</li>
              <li><strong>Unavailable Water:</strong> Water held too tightly by soil particles for plants to extract.
              </li>
            </ul>
            <p class="soil-tip"><strong>Soil Tip:</strong> <span id="soil-water-tip">Loamy soils typically have the most
                plant-available water.</span></p>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h4>Understanding Soil Water Characteristics</h4>
        <div class="info-grid">
          <div class="info-item">
            <h5>Field Capacity</h5>
            <p>The amount of water content held in soil after excess water has drained away. This represents the upper
              limit of water available to plants.</p>
          </div>
          <div class="info-item">
            <h5>Wilting Point</h5>
            <p>The minimum soil moisture at which a plant wilts and cannot recover. Below this point, plants cannot
              extract water from the soil.</p>
          </div>
          <div class="info-item">
            <h5>Plant Available Water</h5>
            <p>The amount of water available for plant use, calculated as the difference between field capacity and
              wilting point.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="recommendations-container">
      <h3>Crop Recommendations</h3>
      <div class="recommendations-content">
        <div class="recommendation-section">
          <h4>Suitable Crops</h4>
          <ul id="suitable-crops-list">
            <li>Loading...</li>
          </ul>
        </div>
        <div class="recommendation-section">
          <h4>Limitations</h4>
          <ul id="limitations-list">
            <li>Loading...</li>
          </ul>
        </div>
        <div class="recommendation-section">
          <h4>Management Tips</h4>
          <ul id="management-tips-list">
            <li>Loading...</li>
          </ul>
        </div>
      </div>
    </div>
  </main>

  <!-- Plan-based Notification Banner -->
  <div class="notification-banner" id="notificationBanner" style="display: none;">
    <div class="notification-content">
      <span class="notification-icon">⚠️</span>
      <span class="notification-message" id="notificationMessage"></span>
      <button class="notification-close" onclick="hideNotificationBanner()">×</button>
    </div>
  </div>

  <!-- Plan Upgrade Modal -->
  <div class="modal-overlay" id="planUpgradeModal" style="display: none;">
    <div class="modal-content plan-upgrade-modal">
      <div class="modal-header">
        <h2 id="upgradeModalTitle">Upgrade Your Plan</h2>
        <button class="modal-close" onclick="hidePlanUpgradeModal()">×</button>
      </div>
      <div class="modal-body">
        <div class="upgrade-message" id="upgradeMessage">
          <p>Unlock advanced features with a plan upgrade!</p>
        </div>

        <div class="plan-comparison">
          <div class="plan-card current-plan" id="currentPlanCard">
            <h3>FREE</h3>
            <div class="plan-price">$0/month</div>
            <ul class="plan-features">
              <li>✓ Basic soil analysis</li>
              <li>✓ 50 analyses per month</li>
              <li>✓ Soil texture classification</li>
              <li>✓ Basic water characteristics</li>
            </ul>
          </div>

          <div class="plan-card recommended-plan" id="recommendedPlanCard">
            <h3>PROFESSIONAL</h3>
            <div class="plan-price">$29/month</div>
            <div class="plan-badge-popular">Most Popular</div>
            <ul class="plan-features">
              <li>✓ Everything in FREE</li>
              <li>✓ 1,000 analyses per month</li>
              <li>✓ Advanced soil parameters</li>
              <li>✓ Batch analysis</li>
              <li>✓ Analysis history</li>
              <li>✓ Export results</li>
            </ul>
            <button class="btn-upgrade" onclick="handlePlanUpgrade('PROFESSIONAL')">
              Upgrade to Professional
            </button>
          </div>

          <div class="plan-card enterprise-plan">
            <h3>ENTERPRISE</h3>
            <div class="plan-price">$99/month</div>
            <ul class="plan-features">
              <li>✓ Everything in PROFESSIONAL</li>
              <li>✓ Unlimited analyses</li>
              <li>✓ Gravel content analysis</li>
              <li>✓ Salinity effects</li>
              <li>✓ Priority support</li>
              <li>✓ API access</li>
            </ul>
            <button class="btn-upgrade enterprise" onclick="handlePlanUpgrade('ENTERPRISE')">
              Upgrade to Enterprise
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Feature Upgrade Prompts -->
  <div class="feature-upgrade-prompt" id="featureUpgradePrompt" style="display: none;">
    <div class="prompt-content">
      <div class="prompt-icon">🔒</div>
      <div class="prompt-text">
        <h4 id="promptTitle">Professional Feature</h4>
        <p id="promptMessage">This feature is available with Professional plan.</p>
      </div>
      <div class="prompt-actions">
        <button class="btn-upgrade-small" onclick="showPlanUpgradePrompt()">Upgrade</button>
        <button class="btn-dismiss" onclick="hideFeatureUpgradePrompt()">Dismiss</button>
      </div>
    </div>
  </div>

  <!-- Authentication Modals -->
  <div class="modal-overlay" id="authModalOverlay" style="display: none;">
    <!-- Login Modal -->
    <div class="modal-content auth-modal" id="loginModal" style="display: none;">
      <div class="modal-header">
        <h2>Login to FlahaSoil</h2>
        <button class="modal-close" onclick="hideAuthModals()">&times;</button>
      </div>
      <div class="modal-body">
        <form id="loginForm" onsubmit="handleLogin(event)">
          <div class="form-group">
            <label for="loginEmail">Email:</label>
            <input type="email" id="loginEmail" required>
          </div>
          <div class="form-group">
            <label for="loginPassword">Password:</label>
            <input type="password" id="loginPassword" required>
          </div>
          <button type="submit" class="btn-primary">Login</button>
          <div class="auth-error" id="loginError" style="display: none;"></div>
        </form>
        <div class="auth-links">
          <p>Don't have an account? <a href="#" onclick="showSignupModal()">Sign up here</a></p>
        </div>
      </div>
    </div>

    <!-- Signup Modal -->
    <div class="modal-content auth-modal" id="signupModal" style="display: none;">
      <div class="modal-header">
        <h2>Sign Up for FlahaSoil</h2>
        <button class="modal-close" onclick="hideAuthModals()">&times;</button>
      </div>
      <div class="modal-body">
        <form id="signupForm" onsubmit="handleSignup(event)">
          <div class="form-group">
            <label for="signupName">Name:</label>
            <input type="text" id="signupName" required>
          </div>
          <div class="form-group">
            <label for="signupEmail">Email:</label>
            <input type="email" id="signupEmail" required>
          </div>
          <div class="form-group">
            <label for="signupPassword">Password:</label>
            <input type="password" id="signupPassword" required minlength="6">
          </div>

          <!-- Plan Selection -->
          <div class="form-group">
            <label>Choose Your Plan:</label>
            <div class="plan-selection">
              <div class="plan-option">
                <input type="radio" id="planFree" name="selectedPlan" value="FREE" checked>
                <label for="planFree" class="plan-option-label">
                  <div class="plan-option-header">
                    <span class="plan-name">FREE</span>
                    <span class="plan-price">$0/month</span>
                  </div>
                  <div class="plan-option-features">
                    <small>50 analyses/month, Basic features</small>
                  </div>
                </label>
              </div>

              <div class="plan-option">
                <input type="radio" id="planPro" name="selectedPlan" value="PROFESSIONAL">
                <label for="planPro" class="plan-option-label recommended">
                  <div class="plan-option-header">
                    <span class="plan-name">PROFESSIONAL</span>
                    <span class="plan-price">$29/month</span>
                  </div>
                  <div class="plan-option-features">
                    <small>1,000 analyses/month, Advanced features</small>
                  </div>
                  <div class="plan-popular-badge">Most Popular</div>
                </label>
              </div>

              <div class="plan-option">
                <input type="radio" id="planEnterprise" name="selectedPlan" value="ENTERPRISE">
                <label for="planEnterprise" class="plan-option-label">
                  <div class="plan-option-header">
                    <span class="plan-name">ENTERPRISE</span>
                    <span class="plan-price">$99/month</span>
                  </div>
                  <div class="plan-option-features">
                    <small>Unlimited analyses, All features</small>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <button type="submit" class="btn-primary">Sign Up</button>
          <div class="auth-error" id="signupError" style="display: none;"></div>
        </form>
        <div class="auth-links">
          <p>Already have an account? <a href="#" onclick="showLoginModal()">Login here</a></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="flaha-footer">
    <div class="footer-content">
      <p>&copy; 2024 Flaha Agri Tech. All rights reserved. | Precision Agriculture Division</p>
      <p>Powered by FlahaSoil - Advanced Soil Analysis Technology</p>
    </div>
  </footer>

  <script type="text/javascript" src="./assets/js/soilCalculations.js"></script>
  <script type="text/javascript" src="./assets/js/apiClient.js"></script>
  <script type="text/javascript" src="./assets/js/main.js"></script>

  <!-- Entry Point Control Script -->
  <script>
    // Check if user should be redirected to landing page
    document.addEventListener('DOMContentLoaded', function () {
      // Check if user came directly to index.html without authentication
      const token = localStorage.getItem('flahasoil_token');
      const fromLanding = sessionStorage.getItem('from_landing');

      // If no token and didn't come from landing, redirect to landing
      if (!token && !fromLanding) {
        window.location.href = './landing.html';
        return;
      }

      // Clear the session flag
      sessionStorage.removeItem('from_landing');
    });
  </script>
</body>

</html>