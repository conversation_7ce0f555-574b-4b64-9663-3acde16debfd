<!DOCTYPE html>
<html>

<head>
    <title>FlahaSoil Plan System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        button {
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
        }

        .user-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <h1>FlahaSoil Plan System Test</h1>

    <div class="test-section">
        <h2>Authentication Test</h2>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testSignup()">Test Signup</button>
        <button onclick="testLogout()">Test Logout</button>
        <div id="authResults"></div>
    </div>

    <div class="test-section">
        <h2>Plan Features Test</h2>
        <button onclick="testBasicAnalysis()">Test Basic Analysis (FREE)</button>
        <button onclick="testAdvancedAnalysis()">Test Advanced Analysis (PRO)</button>
        <button onclick="testBatchAnalysis()">Test Batch Analysis (PRO)</button>
        <button onclick="testEnterpriseAnalysis()">Test Enterprise Analysis (ENT)</button>
        <div id="planResults"></div>
    </div>

    <div class="test-section">
        <h2>Usage Limits Test</h2>
        <button onclick="testUsageLimits()">Test Usage Limits</button>
        <button onclick="resetUsage()">Reset Usage Counter</button>
        <div id="usageResults"></div>
    </div>

    <div class="test-section">
        <h2>Current User Info</h2>
        <div class="user-info" id="userInfo">Not logged in</div>
        <button onclick="refreshUserInfo()">Refresh User Info</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';
        let authToken = localStorage.getItem('token');

        // Update user info display
        function refreshUserInfo() {
            if (!authToken) {
                document.getElementById('userInfo').innerHTML = 'Not logged in';
                return;
            }

            fetch(`${API_BASE_URL}/auth/verify`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.user;
                        document.getElementById('userInfo').innerHTML = `
                        <strong>Name:</strong> ${user.name}<br>
                        <strong>Email:</strong> ${user.email}<br>
                        <strong>Plan:</strong> ${user.tier}<br>
                        <strong>Usage:</strong> ${user.usageCount || 0}/${getPlanLimit(user.tier)}<br>
                        <strong>Plan Selected:</strong> ${user.planSelectedAt ? new Date(user.planSelectedAt).toLocaleDateString() : 'Not set'}
                    `;
                    } else {
                        document.getElementById('userInfo').innerHTML = 'Error loading user info';
                    }
                })
                .catch(error => {
                    document.getElementById('userInfo').innerHTML = 'Error: ' + error.message;
                });
        }

        function getPlanLimit(tier) {
            switch (tier) {
                case 'FREE': return '50';
                case 'PROFESSIONAL': return '1000';
                case 'ENTERPRISE': return 'Unlimited';
                default: return 'Unknown';
            }
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
        }

        // Authentication tests
        function testLogin() {
            const email = prompt('Enter email:') || '<EMAIL>';
            const password = prompt('Enter password:') || 'password123';

            fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        authToken = data.token;
                        localStorage.setItem('token', authToken);
                        addResult('authResults', `Login successful! Plan: ${data.user.tier}`, 'success');
                        refreshUserInfo();
                    } else {
                        addResult('authResults', `Login failed: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('authResults', `Login error: ${error.message}`, 'error');
                });
        }

        function testSignup() {
            const name = prompt('Enter name:') || 'Test User';
            const email = prompt('Enter email:') || `test${Date.now()}@example.com`;
            const password = prompt('Enter password:') || 'password123';
            const selectedPlan = prompt('Enter plan (FREE/PROFESSIONAL/ENTERPRISE):') || 'FREE';

            fetch(`${API_BASE_URL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, email, password, selectedPlan })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        authToken = data.token;
                        localStorage.setItem('token', authToken);
                        addResult('authResults', `Signup successful! Plan: ${data.user.tier}`, 'success');
                        refreshUserInfo();
                    } else {
                        addResult('authResults', `Signup failed: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('authResults', `Signup error: ${error.message}`, 'error');
                });
        }

        function testLogout() {
            authToken = null;
            localStorage.removeItem('token');
            addResult('authResults', 'Logged out successfully', 'success');
            refreshUserInfo();
        }

        // Plan feature tests
        function testBasicAnalysis() {
            if (!authToken) {
                addResult('planResults', 'Please login first', 'error');
                return;
            }

            const soilData = {
                clay: 30,
                sand: 40,
                silt: 30,
                organicMatter: 2.5,
                bulkDensity: 1.3
            };

            fetch(`${API_BASE_URL}/soil/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(soilData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addResult('planResults', 'Basic analysis successful', 'success');
                        refreshUserInfo();
                    } else {
                        addResult('planResults', `Analysis failed: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('planResults', `Analysis error: ${error.message}`, 'error');
                });
        }

        function testAdvancedAnalysis() {
            if (!authToken) {
                addResult('planResults', 'Please login first', 'error');
                return;
            }

            const soilData = {
                clay: 30,
                sand: 40,
                silt: 30,
                organicMatter: 2.5,
                bulkDensity: 1.3,
                gravelContent: 5,
                electricalConductivity: 0.8
            };

            fetch(`${API_BASE_URL}/soil/analyze-advanced`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(soilData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addResult('planResults', 'Advanced analysis successful', 'success');
                        refreshUserInfo();
                    } else {
                        addResult('planResults', `Advanced analysis failed: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('planResults', `Advanced analysis error: ${error.message}`, 'error');
                });
        }

        function testBatchAnalysis() {
            if (!authToken) {
                addResult('planResults', 'Please login first', 'error');
                return;
            }

            const batchData = [
                { clay: 30, sand: 40, silt: 30, organicMatter: 2.5, bulkDensity: 1.3 },
                { clay: 25, sand: 45, silt: 30, organicMatter: 3.0, bulkDensity: 1.2 }
            ];

            fetch(`${API_BASE_URL}/soil/analyze-batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({ samples: batchData })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addResult('planResults', `Batch analysis successful (${data.results.length} samples)`, 'success');
                        refreshUserInfo();
                    } else {
                        addResult('planResults', `Batch analysis failed: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('planResults', `Batch analysis error: ${error.message}`, 'error');
                });
        }

        function testEnterpriseAnalysis() {
            testAdvancedAnalysis(); // Enterprise uses same endpoint but with full features
        }

        function testUsageLimits() {
            if (!authToken) {
                addResult('usageResults', 'Please login first', 'error');
                return;
            }

            // Test multiple rapid requests to hit usage limits
            addResult('usageResults', 'Testing usage limits...', 'info');

            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(
                    fetch(`${API_BASE_URL}/soil/analyze`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify({
                            clay: 30 + i,
                            sand: 40 - i,
                            silt: 30,
                            organicMatter: 2.5,
                            bulkDensity: 1.3
                        })
                    }).then(response => response.json())
                );
            }

            Promise.all(promises).then(results => {
                const successful = results.filter(r => r.success).length;
                const failed = results.filter(r => !r.success).length;
                addResult('usageResults', `Completed ${successful} successful, ${failed} failed requests`, 'info');
                refreshUserInfo();
            });
        }

        function resetUsage() {
            if (!authToken) {
                addResult('usageResults', 'Please login first', 'error');
                return;
            }

            // This would require an admin endpoint in real implementation
            addResult('usageResults', 'Usage reset feature would require admin privileges', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function () {
            refreshUserInfo();
        });
    </script>
</body>

</html>